import { getListPage, getCatalogList, getModelList } from "../../service/api";
import { Logger } from "../../utils/logger";
import { isAuthReady } from "../../config";
import * as path from "path";
import * as fs from "fs";
import * as vscode from "vscode";
import { getCurrentEnv } from "../../utils/globalContext";

export class DataService {
	// 大模型数据缓存
	private modelListCache: {
		data: any[];
		timestamp: number;
	} | null = null;

	// 缓存有效期：2分钟（120000毫秒）
	private readonly CACHE_DURATION = 120000;

	// API服务数据缓存
	private apiListCache: { data: any[], timestamp: number } | null = null;

	// 扩展上下文，用于获取资源路径
	private extensionContext?: vscode.ExtensionContext;

	constructor(extensionContext?: vscode.ExtensionContext) {
		this.extensionContext = extensionContext;
	}

	public async fetchListData(query = "", pageNum = 1, pageSize = 50, catalog = ""): Promise<any[]> {
		try {
			// 检查认证信息是否就绪
			const authReady = isAuthReady();
			if (!authReady) {
				Logger.error(`[DataService] 认证信息未就绪，无法进行API调用`);
				throw new Error('认证信息未就绪，请先登录并配置认证信息');
			}

			let allApiData: any[] = [];
			const currentTime = Date.now();

			// 检查缓存是否有效（2分钟内）
			if (this.apiListCache &&
				(currentTime - this.apiListCache.timestamp) < this.CACHE_DURATION) {
				Logger.info(`[DataService] 使用缓存的API服务数据`);
				allApiData = this.apiListCache.data;
			} else {
				// 缓存过期或不存在，重新请求数据
				Logger.info(`[DataService] 缓存过期或不存在，重新请求API服务数据`);

				// 构建请求参数 - 不传query参数，获取全部数据用于前端搜索
				const params: any = { pageNum: 1, pageSize: 1000 };
				// 注意：这里不传query参数，因为需要先获取全部数据再过滤
				if (catalog) {
					params.catalog = catalog;
				}

				const res = await getListPage(params);

				// 处理响应数据，如果是字符串则尝试解析
				let responseData = res.data;
				if (typeof responseData === 'string') {
					try {
						responseData = JSON.parse(responseData);
					} catch (parseError) {
						Logger.error(`[DataService] 解析响应数据失败: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
						return [];
					}
				}

				const data = responseData?.data || responseData;
				if (data && typeof data === 'object' && 'records' in data && Array.isArray(data.records)) {
					allApiData = data.records;

					// 更新缓存
					this.apiListCache = {
						data: allApiData,
						timestamp: currentTime
					};
					Logger.info(`[DataService] API服务数据已缓存，共 ${allApiData.length} 条记录`);
				} else {
					Logger.warn(`[DataService] 数据格式不正确或为空`);
					return [];
				}
			}

			// 如果有查询条件，在前端进行数据过滤
			let processedData = allApiData;
			if (query && query.trim()) {
				const queryLower = query.trim().toLowerCase();
				processedData = allApiData.filter(item => {
					const name = (item.name || '').toLowerCase();
					const description = (item.pluginDesc || '').toLowerCase();
					return name.includes(queryLower) || description.includes(queryLower);
				});
			}

			// 简单的分页处理（因为是前端过滤，需要手动分页）
			const startIndex = (pageNum - 1) * pageSize;
			const endIndex = startIndex + pageSize;
			return processedData.slice(startIndex, endIndex);

		} catch (e) {
			// 安全地记录错误信息
			if (e instanceof Error) {
				Logger.error(`[DataService] 获取列表数据失败: ${e.message}`);
			} else {
				Logger.error(`[DataService] 获取列表数据失败: ${String(e)}`);
			}
			return [];
		}
	}

	public async fetchModelListData(query = "", pageNum = 1, pageSize = 50): Promise<any[]> {
		try {
			// 检查认证信息是否就绪
			const authReady = isAuthReady();
			if (!authReady) {
				Logger.error(`[DataService] 认证信息未就绪，无法进行API调用`);
				throw new Error('认证信息未就绪，请先登录并配置认证信息');
			}

			// 获取当前环境，判断是否为内部用户
			const currentEnv = getCurrentEnv();
			const isInternalUser = currentEnv === "inner";

			Logger.info(`[DataService] 当前环境: ${currentEnv}, 是否内部用户: ${isInternalUser}`);

			// 如果是内部用户，直接返回京东大模型网关数据
			if (isInternalUser) {
				return this.getInternalUserModelData(query, pageNum, pageSize);
			}

			// 外部用户从JSON文件读取数据
			return this.getExternalUserModelData(query, pageNum, pageSize);

			/* 注释掉原来的API调用逻辑，保留以备后用
			let allModelData: any[] = [];
			const currentTime = Date.now();

			// 检查缓存是否有效（2分钟内）
			if (this.modelListCache &&
				(currentTime - this.modelListCache.timestamp) < this.CACHE_DURATION) {
				Logger.info(`[DataService] 使用缓存的大模型数据`);
				allModelData = this.modelListCache.data;
			} else {
				// 缓存过期或不存在，重新请求数据
				Logger.info(`[DataService] 缓存过期或不存在，重新请求大模型数据`);

				// 构建请求参数 - 大模型接口暂时不支持query参数，所以先请求全部数据
				const params: any = { pageNum, pageSize };
				// 注意：这里不传query参数，因为需要先获取全部数据再过滤

				const res = await getModelList(params);

				// 处理响应数据，如果是字符串则尝试解析
				let responseData = res.data;
				if (typeof responseData === 'string') {
					try {
						responseData = JSON.parse(responseData);
					} catch (parseError) {
						Logger.error(`[DataService] 解析大模型响应数据失败: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
						return [];
					}
				}

				const data = responseData?.data || responseData;
				// 大模型接口返回的数据直接是数组，不像插件接口有records字段
				if (Array.isArray(data)) {
					// 将大模型数据格式转换为与插件数据一致的格式
					allModelData = data.map(item => ({
						name: item.label || item.name,
						pluginId: item.chatApiModel || item.id,
						pluginDesc: item.description || item.pluginDesc,
						iconUrl: item.avatar || item.iconUrl,
						pluginType: 'model',
						catalog: '大模型',
						// 保留原始数据以备后用
						...item
					}));

					// 更新缓存
					this.modelListCache = {
						data: allModelData,
						timestamp: currentTime
					};
					Logger.info(`[DataService] 大模型数据已缓存，共 ${allModelData.length} 条记录`);
				} else {
					Logger.warn(`[DataService] 大模型数据格式不正确或为空`);
					return [];
				}
			}

			// 如果有查询条件，在前端进行数据过滤
			let processedData = allModelData;
			if (query && query.trim()) {
				const queryLower = query.trim().toLowerCase();
				processedData = allModelData.filter(item => {
					const name = (item.name || '').toLowerCase();
					const description = (item.pluginDesc || '').toLowerCase();
					return name.includes(queryLower) || description.includes(queryLower);
				});
			}

			// 简单的分页处理（因为是前端过滤，需要手动分页）
			const startIndex = (pageNum - 1) * pageSize;
			const endIndex = startIndex + pageSize;
			return processedData.slice(startIndex, endIndex);
			*/

		} catch (e) {
			// 安全地记录错误信息
			if (e instanceof Error) {
				Logger.error(`[DataService] 获取大模型列表数据失败: ${e.message}`);
			} else {
				Logger.error(`[DataService] 获取大模型列表数据失败: ${String(e)}`);
			}
			return [];
		}
	}

	/**
	 * 获取内部用户的大模型数据（只返回京东大模型网关）
	 * @param query 搜索关键词
	 * @param pageNum 页码
	 * @param pageSize 每页大小
	 */
	private getInternalUserModelData(query = "", pageNum = 1, pageSize = 50): any[] {
		Logger.info(`[DataService] 内部用户，返回京东大模型网关数据`);

		// 获取图标的完整路径
		let iconUrl = "jdmodel.png";
		if (this.extensionContext) {
			try {
				const iconPath = vscode.Uri.joinPath(this.extensionContext.extensionUri, 'resources', 'images', 'jdmodel.png');
				// 注意：这里我们先使用相对路径，在webview中会被正确处理
				iconUrl = iconPath.toString();
				Logger.info(`[DataService] 内部用户图标路径: ${iconUrl}`);
			} catch (error) {
				Logger.warn(`[DataService] 获取图标路径失败: ${error instanceof Error ? error.message : String(error)}`);
				iconUrl = "images/jdmodel.png"; // 回退到相对路径
			}
		}

		// 京东大模型网关的固定数据
		const jdModelData = {
			name: "京东大模型网关",
			pluginId: "jd-model-gateway",
			pluginDesc: "由京东集团技委会主导建设，提供多种大模型的统一网关接口服务",
			iconUrl: iconUrl,
			pluginType: 'model',
			catalog: '大模型',
			// 额外的标识字段
			isJdInternal: true,
			label: "京东大模型网关",
			description: "由京东集团技委会主导建设，提供多种大模型的统一网关接口服务",
			avatar: iconUrl,
			chatApiModel: "jd-model-gateway"
		};

		// 如果有搜索条件，进行过滤
		let filteredData = [jdModelData];
		if (query && query.trim()) {
			const queryLower = query.trim().toLowerCase();
			const name = jdModelData.name.toLowerCase();
			const description = jdModelData.pluginDesc.toLowerCase();

			if (!name.includes(queryLower) && !description.includes(queryLower)) {
				filteredData = [];
			}
		}

		// 简单的分页处理
		const startIndex = (pageNum - 1) * pageSize;
		const endIndex = startIndex + pageSize;
		return filteredData.slice(startIndex, endIndex);
	}

	/**
	 * 获取外部用户的大模型数据（从JSON文件读取）
	 * @param query 搜索关键词
	 * @param pageNum 页码
	 * @param pageSize 每页大小
	 */
	private getExternalUserModelData(query = "", pageNum = 1, pageSize = 50): any[] {
		try {
			Logger.info(`[DataService] 外部用户，从JSON文件读取大模型数据`);

			// 读取JSON文件
			const resourcesPath = path.join(__dirname, '..', '..', '..', 'resources', 'data', 'external-models.json');
			const jsonData = fs.readFileSync(resourcesPath, 'utf8');
			const externalModels = JSON.parse(jsonData);

			Logger.info(`[DataService] 从JSON文件读取到 ${externalModels.length} 个外部用户大模型`);

			// 如果有搜索条件，进行过滤
			let filteredData = externalModels;
			if (query && query.trim()) {
				const queryLower = query.trim().toLowerCase();
				filteredData = externalModels.filter((item: any) => {
					const name = (item.name || '').toLowerCase();
					const description = (item.pluginDesc || '').toLowerCase();
					return name.includes(queryLower) || description.includes(queryLower);
				});
			}

			// 简单的分页处理
			const startIndex = (pageNum - 1) * pageSize;
			const endIndex = startIndex + pageSize;
			return filteredData.slice(startIndex, endIndex);

		} catch (error) {
			Logger.error(`[DataService] 读取外部用户大模型JSON文件失败: ${error instanceof Error ? error.message : String(error)}`);
			return [];
		}
	}

	/**
	 * 清除大模型数据缓存，强制下次请求重新获取数据
	 */
	public clearModelListCache(): void {
		this.modelListCache = null;
		Logger.info(`[DataService] 大模型数据缓存已清除`);
	}

	/**
	 * 清除API服务数据缓存，强制下次请求重新获取数据
	 */
	public clearApiListCache(): void {
		this.apiListCache = null;
		Logger.info(`[DataService] API服务数据缓存已清除`);
	}

	/**
	 * 清除所有数据缓存
	 */
	public clearAllCache(): void {
		this.clearModelListCache();
		this.clearApiListCache();
		Logger.info(`[DataService] 所有数据缓存已清除`);
	}

	/**
	 * 获取"全部"分类的数据（大模型 + API 服务数据拼接）
	 * @param query 搜索关键词
	 * @param pageNum 页码
	 * @param pageSize 每页大小
	 */
	public async fetchAllData(query = "", pageNum = 1, pageSize = 50): Promise<any[]> {
		try {
			// 并行获取大模型和API服务数据
			const [modelData, apiData] = await Promise.all([
				this.fetchModelListData(query, 1, 1000), // 获取所有大模型数据
				this.fetchListData(query, 1, 1000, ""), // 获取所有API服务数据（不传catalog参数）
			]);

			// 合并数据，大模型数据在前
			let allData = [...modelData, ...apiData];

			// 如果数据有排序字段，确保大模型优先（已经通过数组顺序实现）
			// 可以根据需要添加更复杂的排序逻辑

			// 手动分页处理
			const startIndex = (pageNum - 1) * pageSize;
			const endIndex = startIndex + pageSize;
			return allData.slice(startIndex, endIndex);

		} catch (e) {
			if (e instanceof Error) {
				Logger.error(`[DataService] 获取全部数据失败: ${e.message}`);
			} else {
				Logger.error(`[DataService] 获取全部数据失败: ${String(e)}`);
			}
			return [];
		}
	}

	public async fetchCatalogList(catalog_type = "plugin"): Promise<any[]> {
		try {
			// 检查认证信息是否就绪
			const authReady = isAuthReady();
			if (!authReady) {
				Logger.error(`[DataService] 认证信息未就绪，无法进行API调用`);
				throw new Error('认证信息未就绪，请先登录并配置认证信息');
			}

			const res = await getCatalogList(catalog_type);

			// 处理响应数据，如果是字符串则尝试解析
			let responseData = res.data;
			if (typeof responseData === 'string') {
				try {
					responseData = JSON.parse(responseData);
				} catch (parseError) {
					Logger.error(`[DataService] 解析响应数据失败: ${parseError instanceof Error ? parseError.message : String(parseError)}`);
					return [];
				}
			}

			const data = responseData?.data || responseData;
			if (data && Array.isArray(data)) {
				return data;
			}

			Logger.warn(`[DataService] 分类数据格式不正确或为空`);
			return [];
		} catch (e) {
			// 安全地记录错误信息
			if (e instanceof Error) {
				Logger.error(`[DataService] 获取分类列表失败: ${e.message}`);
			} else {
				Logger.error(`[DataService] 获取分类列表失败: ${String(e)}`);
			}
			return [];
		}
	}
}
