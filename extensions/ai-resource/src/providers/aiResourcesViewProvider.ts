import * as vscode from "vscode";
import { MarkdownProvider } from "./markdownProvider";
import { Logger } from "../utils/logger";
import { WebviewContentGenerator } from "./webview/webviewContentGenerator";
import { MessageHandler } from "./handlers/messageHandler";
import { AuthenticationManager } from "./auth/authenticationManager";
import { DataService } from "./services/dataService";
import { isAuthReady } from "../config";
import { AnalyticsHelper } from "../utils/analyticsHelper";

export class AIResourcesViewProvider implements vscode.WebviewViewProvider {
	private webviewView?: vscode.WebviewView;
	private context: vscode.ExtensionContext;
	private mdProvider: MarkdownProvider;
	private authManager: AuthenticationManager;
	private webviewGenerator: WebviewContentGenerator;
	private messageHandler: MessageHandler;
	private dataService: DataService;
	private analytics: AnalyticsHelper;

	constructor(context: vscode.ExtensionContext, mdProvider: MarkdownProvider) {
		this.context = context;
		this.mdProvider = mdProvider;
		this.authManager = new AuthenticationManager();
		this.webviewGenerator = new WebviewContentGenerator(context);
		this.messageHandler = new MessageHandler(mdProvider, context);
		this.dataService = new DataService(context);
		this.analytics = AnalyticsHelper.getInstance();
	}

	public setInitializing(initializing: boolean) {
		this.authManager.setInitializing(initializing);
	}

	public async refresh() {
		if (this.webviewView) {
			Logger.info("[AIResourcesViewProvider] 刷新 webview - 重新生成 HTML 内容");

			// 重新生成 webview 的 HTML 内容（因为登录状态可能已变化）
			this.webviewView.webview.html = await this.getWebviewContent();

			// 如果用户已登录，还需要重新加载数据
			const isLoggedIn = await this.authManager.isLoggedIn();
			if (isLoggedIn) {
				Logger.info("[AIResourcesViewProvider] 用户已登录，重新加载数据");
				await this.loadData();
			} else {
				Logger.info("[AIResourcesViewProvider] 用户未登录，显示登录页面");
			}
		}
	}

	/**
	 * 刷新数据（不重新加载整个webview）
	 * 就像用户手动刷新一样，重新触发完整的数据加载流程
	 */
	public async refreshData(): Promise<void> {
		Logger.info(`[AIResourcesViewProvider] refreshData 被调用`);

		if (!this.webviewView) {
			Logger.warn(`[AIResourcesViewProvider] webviewView 未准备好，跳过数据刷新`);
			return;
		}

		// 重新触发完整的数据加载流程，就像用户手动刷新一样
		try {
			Logger.info(`[AIResourcesViewProvider] 开始重新加载数据...`);

			// 检查登录状态
			const isLoggedIn = await this.authManager.isLoggedIn();
			Logger.info(`[AIResourcesViewProvider] 当前登录状态: ${isLoggedIn}`);

			if (isLoggedIn) {
				// 重新初始化 API Key（这会更新认证状态）
				await this.authManager.initializeApiKey();

				// 加载数据
				await this.loadData();
			} else {
				Logger.info(`[AIResourcesViewProvider] 用户未登录，跳过数据加载`);
			}
		} catch (error) {
			Logger.error(`[AIResourcesViewProvider] 刷新数据失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	async resolveWebviewView(webviewView: vscode.WebviewView): Promise<void> {
		Logger.info(`[AIResourcesViewProvider] resolveWebviewView 开始`);
		this.webviewView = webviewView;
		this.webviewGenerator.setWebviewView(webviewView);

		webviewView.webview.options = {
			enableScripts: true,
		};

		// 先快速显示UI，再异步初始化API key
		webviewView.webview.html = await this.getWebviewContent();
		Logger.info(`[AIResourcesViewProvider] webview HTML已设置`);

		// 追踪主视图页面浏览
		await this.analytics.trackPageView('ai_resource_list', 'view');

		// 异步初始化API key，不阻塞UI显示
		this.authManager.initializeApiKey().catch((error) => {
			Logger.error(`[AIResourcesViewProvider] API key初始化失败: ${error}`);
		});

		const isLoggedIn = await this.authManager.isLoggedIn();
		Logger.info(`[AIResourcesViewProvider] 登录状态: ${isLoggedIn}`);

		if (!isLoggedIn) {
			Logger.info(`[AIResourcesViewProvider] 用户未登录`);
		}

		webviewView.webview.onDidReceiveMessage(async (message) => {
			const isLoggedIn = await this.authManager.isLoggedIn();
			if (!isLoggedIn) {
				if (message.type === "login") {
					await this.authManager.handleLogin();
				}
				return;
			}
			switch (message.type) {
				case "select":
					await this.messageHandler.handleSelectMessage(message);
					break;
				case "openSettings":
					// 追踪设置按钮点击
					await this.analytics.trackSettingsButtonClick('open_settings', {
						page: 'ai_resource_list',
						action: 'open_settings_panel'
					});
					this.messageHandler.handleOpenSettingsMessage();
					break;
				case "search":
					// 追踪搜索行为
					await this.analytics.trackSearch(
						message.query || '',
						message.catalog || 'all',
						undefined
					);

					// 根据分类决定调用哪个接口
					if (message.catalog === "大模型") {
						this.dataService.fetchModelListData(
							message.query,
							message.pageNum,
							message.pageSize,
						).then((records) => {
							const processedRecords = this.processIconUrls(webviewView.webview, records);
							webviewView.webview.postMessage({ type: "listData", records: processedRecords });
						}).catch((error) => {
							Logger.error(`[AIResourcesViewProvider] 大模型搜索失败: ${error instanceof Error ? error.message : String(error)}`);
							webviewView.webview.postMessage({ type: "listData", records: [] });
						});
					} else if (message.catalog === "API 服务") {
						// API 服务使用原来的全部接口
						this.dataService.fetchListData(
							message.query,
							message.pageNum,
							message.pageSize,
							"", // API 服务不传catalog参数，使用原来的全部接口
						).then((records) => {
							const processedRecords = this.processIconUrls(webviewView.webview, records);
							webviewView.webview.postMessage({ type: "listData", records: processedRecords });
						}).catch((error) => {
							Logger.error(`[AIResourcesViewProvider] API 服务搜索失败: ${error instanceof Error ? error.message : String(error)}`);
							webviewView.webview.postMessage({ type: "listData", records: [] });
						});
					} else if (message.catalog === "" || message.catalog === "全部") {
						// 全部分类：大模型 + API 服务数据拼接
						this.dataService.fetchAllData(
							message.query,
							message.pageNum,
							message.pageSize,
						).then((records) => {
							const processedRecords = this.processIconUrls(webviewView.webview, records);
							webviewView.webview.postMessage({ type: "listData", records: processedRecords });
						}).catch((error) => {
							Logger.error(`[AIResourcesViewProvider] 全部数据搜索失败: ${error instanceof Error ? error.message : String(error)}`);
							webviewView.webview.postMessage({ type: "listData", records: [] });
						});
					} else {
						// 其他分类（保留原有逻辑）
						this.dataService.fetchListData(
							message.query,
							message.pageNum,
							message.pageSize,
							message.catalog,
						).then((records) => {
							const processedRecords = this.processIconUrls(webviewView.webview, records);
							webviewView.webview.postMessage({ type: "listData", records: processedRecords });
						}).catch((error) => {
							Logger.error(`[AIResourcesViewProvider] 搜索失败: ${error instanceof Error ? error.message : String(error)}`);
							webviewView.webview.postMessage({ type: "listData", records: [] });
						});
					}
					break;
				case "getList":
					// 追踪 Tab 页签点击
					await this.analytics.trackTabClick(
						message.catalog || '全部',
						undefined,
						{
							query: message.query || '',
							pageNum: message.pageNum || 1,
							pageSize: message.pageSize || 20
						}
					);

					// 根据分类决定调用哪个接口
					if (message.catalog === "大模型") {
						this.dataService.fetchModelListData(
							message.query,
							message.pageNum,
							message.pageSize,
						).then((records) => {
							const processedRecords = this.processIconUrls(webviewView.webview, records);
							webviewView.webview.postMessage({ type: "listData", records: processedRecords });
						}).catch((error) => {
							Logger.error(`[AIResourcesViewProvider] 获取大模型列表失败: ${error instanceof Error ? error.message : String(error)}`);
							webviewView.webview.postMessage({ type: "listData", records: [] });
						});
					} else if (message.catalog === "API 服务") {
						// API 服务使用原来的全部接口
						this.dataService.fetchListData(
							message.query,
							message.pageNum,
							message.pageSize,
							"", // API 服务不传catalog参数，使用原来的全部接口
						).then((records) => {
							const processedRecords = this.processIconUrls(webviewView.webview, records);
							webviewView.webview.postMessage({ type: "listData", records: processedRecords });
						}).catch((error) => {
							Logger.error(`[AIResourcesViewProvider] 获取API服务列表失败: ${error instanceof Error ? error.message : String(error)}`);
							webviewView.webview.postMessage({ type: "listData", records: [] });
						});
					} else if (message.catalog === "" || message.catalog === "全部") {
						// 全部分类：大模型 + API 服务数据拼接
						this.dataService.fetchAllData(
							message.query,
							message.pageNum,
							message.pageSize,
						).then((records) => {
							const processedRecords = this.processIconUrls(webviewView.webview, records);
							webviewView.webview.postMessage({ type: "listData", records: processedRecords });
						}).catch((error) => {
							Logger.error(`[AIResourcesViewProvider] 获取全部数据列表失败: ${error instanceof Error ? error.message : String(error)}`);
							webviewView.webview.postMessage({ type: "listData", records: [] });
						});
					} else {
						// 其他分类（保留原有逻辑）
						this.dataService.fetchListData(
							message.query,
							message.pageNum,
							message.pageSize,
							message.catalog,
						).then((records) => {
							const processedRecords = this.processIconUrls(webviewView.webview, records);
							webviewView.webview.postMessage({ type: "listData", records: processedRecords });
						}).catch((error) => {
							Logger.error(`[AIResourcesViewProvider] 获取列表失败: ${error instanceof Error ? error.message : String(error)}`);
							webviewView.webview.postMessage({ type: "listData", records: [] });
						});
					}
					break;
				case "quote":
					await this.messageHandler.handleQuoteMessage(message);
					break;
				case "generateApp":
					await this.messageHandler.handleGenerateAppMessage(message);
					break;
				case "getCatalogList":
					// 注释掉动态获取分类列表的功能，改为使用固定分类
					/*
					this.dataService.fetchCatalogList(message.catalog_type).then((catalogs) => {
						webviewView.webview.postMessage({ type: "catalogListData", catalogs });
					}).catch((error) => {
						Logger.error(`[AIResourcesViewProvider] 获取分类列表失败: ${error instanceof Error ? error.message : String(error)}`);
						webviewView.webview.postMessage({ type: "catalogListData", catalogs: [] });
					});
					*/
					// 返回固定的分类列表（实际上前端已经不需要这个数据了）
					webviewView.webview.postMessage({ type: "catalogListData", catalogs: [] });
					break;
			}
		});
	}

	private async getWebviewContent(): Promise<string> {
		const { isJoyCode } = this.authManager.getAuthenticationInfo();
		const isLoggedIn = isJoyCode ? await this.authManager.isLoggedIn() : false;

		Logger.info(`[AIResourcesViewProvider] getWebviewContent - isJoyCode: ${isJoyCode}, isLoggedIn: ${isLoggedIn}`);

		// 如果不是JoyCode环境，显示错误页面
		if (!isJoyCode) {
			Logger.info("[AIResourcesViewProvider] 不是JoyCode环境，显示错误页面");
			return this.webviewGenerator.generateEnvironmentErrorContent();
		}

		if (!isLoggedIn) {
			Logger.info("[AIResourcesViewProvider] 用户未登录，显示登录页面");
			return this.webviewGenerator.generateLoginContent();
		}

		Logger.info("[AIResourcesViewProvider] 用户已登录，显示资源列表页面");
		return this.webviewGenerator.generateResourceListContent();
	}

	/**
	 * 简单的数据加载，不重试
	 */
	private async loadData(): Promise<void> {
		try {
			Logger.info(`[AIResourcesViewProvider] 开始加载数据`);

			// 检查认证状态
			const authReady = isAuthReady();
			Logger.info(`[AIResourcesViewProvider] 认证状态检查: ${authReady ? '就绪' : '未就绪'}`);

			if (!authReady) {
				Logger.warn(`[AIResourcesViewProvider] 认证信息未就绪，跳过数据加载`);
				return;
			}

			const records = await this.dataService.fetchListData();
			Logger.info(`[AIResourcesViewProvider] 获取到 ${records.length} 条记录，发送到webview`);

			// 记录数据样本
			if (records.length > 0) {
				Logger.info(`[AIResourcesViewProvider] loadData数据样本: ${JSON.stringify(records[0])}`);
			}

			if (this.webviewView) {
				const message = { type: "listData", records };
				Logger.info(`[AIResourcesViewProvider] loadData发送消息到webview: ${JSON.stringify({ type: message.type, recordsLength: message.records.length })}`);
				this.webviewView.webview.postMessage(message);
				Logger.info(`[AIResourcesViewProvider] loadData数据已发送到webview`);
			} else {
				Logger.warn(`[AIResourcesViewProvider] webviewView 为空，无法发送数据`);
			}
		} catch (error) {
			Logger.error(`[AIResourcesViewProvider] 数据加载失败: ${error instanceof Error ? error.message : String(error)}`);
		}
	}

	/**
	 * 处理图标URL，将本地资源路径转换为webview可用的URI
	 * @param webview webview实例
	 * @param records 数据记录
	 * @returns 处理后的数据记录
	 */
	private processIconUrls(webview: vscode.Webview, records: any[]): any[] {
		return records.map(record => {
			// 如果iconUrl是本地文件路径（包含file:///或者是相对路径），则转换为webview URI
			if (record.iconUrl && typeof record.iconUrl === 'string') {
				try {
					// 检查是否是本地文件URI或包含jdmodel.png
					if (record.iconUrl.startsWith('file:///') ||
						record.iconUrl.includes('jdmodel.png') ||
						record.iconUrl.includes('resources/jdmodel.png')) {

						let iconUri: vscode.Uri;

						if (record.iconUrl.startsWith('file:///')) {
							iconUri = vscode.Uri.parse(record.iconUrl);
						} else if (record.iconUrl === 'jdmodel.png' || record.iconUrl.includes('jdmodel.png')) {
							// 相对路径或包含jdmodel.png，构建完整路径
							iconUri = vscode.Uri.joinPath(this.context.extensionUri, 'resources', 'images', 'jdmodel.png');
						} else {
							// 其他情况，尝试解析
							iconUri = vscode.Uri.parse(record.iconUrl);
						}

						const webviewUri = webview.asWebviewUri(iconUri);
						Logger.info(`[AIResourcesViewProvider] 转换图标路径: ${record.iconUrl} -> ${webviewUri.toString()}`);

						return {
							...record,
							iconUrl: webviewUri.toString(),
							avatar: webviewUri.toString()
						};
					}
				} catch (error) {
					Logger.warn(`[AIResourcesViewProvider] 转换图标路径失败: ${error instanceof Error ? error.message : String(error)}`);
				}
			}

			return record;
		});
	}

}




