import * as vscode from "vscode";
import { getCurrentApi<PERSON>ey, setCurrent<PERSON>pi<PERSON><PERSON> } from "../../config";
import { Logger } from "../../utils/logger";
import { JoycodeHelper } from "../../utils/joycodeHelper";
import { I18n } from "../../utils/i18n";
import { EnvironmentHelper } from "../../utils/globalContext";
import {
	JOYCODE_IS_LOGGED_IN,
	JOYCODE_LOGIN,
	JOYCODE_LOGIN_FALLBACK,
	AI_RESOURCE_CALLBACK_ID
} from "../../constants/commands";

export class AuthenticationManager {
	private isInitializing: boolean = false;

	public setInitializing(initializing: boolean) {
		this.isInitializing = initializing;
		Logger.info(`[AuthenticationManager] 设置初始化状态: ${initializing}`);
	}

	public async isLoggedIn(): Promise<boolean> {
		const callbackId = AI_RESOURCE_CALLBACK_ID;
		try {
			const result = await vscode.commands.executeCommand(JOYCODE_IS_LOGGED_IN, callbackId) as boolean;
			Logger.info(`[AuthenticationManager] 登录状态检查结果: ${result}`);
			return result;
		} catch (error) {
			Logger.error(`[AuthenticationManager] 检查登录状态失败: ${error instanceof Error ? error.message : String(error)}`);
			return false;
		}
	}

	public async initializeApiKey(): Promise<void> {
		const currentKey = getCurrentApiKey();
		const isRemote = !!vscode.env.remoteName;
		const workspaceName = vscode.workspace.name || "unknown";

		Logger.info(
			`[AuthenticationManager] initializeApiKey - 工作区: ${workspaceName}, 初始化状态: ${this.isInitializing}, 远程环境: ${isRemote}, 当前API key存在: ${!!currentKey}`,
		);

		// 如果当前工作区还没有API key，尝试从文件读取
		if (!currentKey) {
			try {
				const apikey = await JoycodeHelper.readApiKey();
				if (typeof apikey === "string" && apikey.trim()) {
					await setCurrentApiKey(apikey);
					Logger.info(
						"[AuthenticationManager] 成功从本地文件读取API key到新工作区",
					);
				} else {
					Logger.info(
						"[AuthenticationManager] 文件中无有效API key，新工作区保持空状态",
					);
					// 不主动清空，让extension.ts中的逻辑处理
				}
			} catch (error) {
				Logger.error("[AuthenticationManager] 读取API key失败: " + error);
				// 不主动清空，让extension.ts中的逻辑处理
			}
		} else {
			Logger.info("[AuthenticationManager] 当前工作区已有API key，保持不变");
		}
	}

	public async handleLogin(): Promise<void> {
		// 尝试多种登录命令，确保兼容性
		try {
			// 首先尝试使用标准的登录命令
			await vscode.commands.executeCommand(JOYCODE_LOGIN);
		} catch (error) {
			Logger.warn("标准登录命令失败，尝试其他方式: " + error);
			try {
				// 如果标准命令失败，尝试其他可能的命令
				await vscode.commands.executeCommand(JOYCODE_LOGIN_FALLBACK);
			} catch (error2) {
				Logger.error("所有登录命令都失败: " + error2);
				vscode.window.showErrorMessage(I18n.t("auth.loginUnavailable"));
			}
		}
	}

	public isJoyCodeEnvironment(): boolean {
		return EnvironmentHelper.isJoyCodeEnvironment();
	}

	public getAuthenticationInfo(): { currentKey: string | undefined, isJoyCode: boolean, isRemote: boolean } {
		const currentKey = getCurrentApiKey();
		const envInfo = EnvironmentHelper.getEnvironmentInfo();

		Logger.info(
			`[AuthenticationManager] getAuthenticationInfo - 远程环境: ${envInfo.isRemote}, JoyCode环境: ${envInfo.isJoyCode}, API key存在: ${!!currentKey}`,
		);

		return {
			currentKey,
			isJoyCode: envInfo.isJoyCode,
			isRemote: envInfo.isRemote
		};
	}
}
