import { I18n } from "../../utils/i18n";
import { getCurrentBaseUrl, getCurrentApiKey } from "../../config";

// 插件工具服务 URL 路径常量
const PLUGIN_TOOL_SERVICE_PATH = "/api/saas/tool/v1/plugin/run";

// Fetch 请求和响应的类型定义
export interface FetchRequestConfig {
	method: string;
	url: string;
	headers: Record<string, string>;
	body?: any;
}

export interface FetchResponseData {
	code: number;
	msg: string;
	data: any;
}

export class MarkdownContentGenerator {
	public generatePluginMarkdown(data: any, options: { includeTable?: boolean } = {}): string {
		const { includeTable = true } = options;
		let mdContent = `# ${I18n.t("markdown.service.title", data.name)}\n\n`;
		mdContent += `${data.pluginDesc || ""}\n\n`;

		if (data.tools && data.tools.length > 0) {
			data.tools.forEach((tool: any) => {
				const nestedStructures: any[] = [];
				const collectNestedStructures = (
					paramName: string,
					children: any[],
					level: number = 0,
				) => {
					// 检查是否是数组类型且只有一个 [Array Item] 子元素
					const hasArrayItem = children.length === 1 && children[0].paramName === "[Array Item]";

					if (hasArrayItem) {
						// 如果是数组类型，直接收集 [Array Item] 的子元素，使用父级数组名称
						const arrayItemChild = children[0];
						if (arrayItemChild.children && arrayItemChild.children.length > 0) {
							nestedStructures.push({
								name: paramName, // 使用数组名称而不是 [Array Item]
								children: arrayItemChild.children,
								level: level,
								isArrayItem: true
							});

							// 递归处理 [Array Item] 的子元素
							arrayItemChild.children.forEach((grandChild: any) => {
								if (grandChild.children && grandChild.children.length > 0) {
									collectNestedStructures(
										grandChild.paramName,
										grandChild.children,
										level + 1,
									);
								}
							});
						}
					} else {
						// 普通对象结构
						nestedStructures.push({
							name: paramName,
							children: children,
							level: level,
						});
						children.forEach((child: any) => {
							if (child.children && child.children.length > 0) {
								collectNestedStructures(
									child.paramName,
									child.children,
									level + 1,
								);
							}
						});
					}
				};

				mdContent += `## ${tool.name}\n\n`;
				mdContent += `${tool.toolDesc || ""}\n\n`;
				// 构建标准的 service URL 格式：{PLUGIN_TOOL_SERVICE_PATH}/{pluginToolId}
				const serviceUrl = tool.pluginToolId ?
					`${PLUGIN_TOOL_SERVICE_PATH}/${tool.pluginToolId}` :
					tool.toolUrl;
				mdContent += `### ${I18n.t("markdown.service.url")}\n\n\`POST ${serviceUrl}\`\n\n`;

				if (tool.requestParams && tool.requestParams.length > 0) {
					mdContent += this.generateRequestParamsMarkdown(tool.requestParams, tool, includeTable);
				}

				if (tool.responseParams && tool.responseParams.length > 0) {
					// 先生成响应参数表格（不包含 Fetch 格式）
					mdContent += this.generateResponseParamsTable(tool.responseParams, collectNestedStructures, includeTable);

					// 然后生成嵌套结构表格
					if (includeTable && nestedStructures.length > 0) {
						mdContent += this.generateNestedStructuresMarkdown(nestedStructures);
					}

					// 最后生成 Fetch 响应格式
					mdContent += this.generateFetchResponseJson(tool.responseParams, tool);
				}
			});
		}

		return mdContent;
	}

	private generateRequestParamsMarkdown(requestParams: any[], tool: any, includeTable: boolean = true): string {
		let mdContent = "";

		if (includeTable) {
			mdContent += `### ${I18n.t("markdown.request.params")}\n\n`;
			mdContent += `| ${I18n.t("markdown.table.paramName")} | ${I18n.t("markdown.table.type")} | ${I18n.t("markdown.table.required")} | ${I18n.t("markdown.table.description")} |\n`;
			mdContent += `| --- | --- | --- | --- |\n`;

			requestParams.forEach((param: any) => {
				const required = param.requiredFlag ? I18n.t("markdown.table.yes") : I18n.t("markdown.table.no");
				const displayType = this.getDisplayType(param.paramName, param.paramType);
				mdContent += `| ${param.paramName} | ${displayType} | ${required} | ${param.paramDesc || ""} |\n`;
			});

			mdContent += "\n";
		}

		// 添加 Fetch 请求配置 JSON，传入 tool 对象
		mdContent += this.generateFetchRequestJson(requestParams, tool);

		if (includeTable) {
			const inputMethod = requestParams.find((p: any) => p.inputMethod)?.inputMethod || "";
			if (inputMethod) {
				mdContent += `- ${I18n.t("markdown.request.type", inputMethod)}\n\n`;
			}
		}

		return mdContent;
	}

	private getDefaultValueByType(type: string): string {
		switch (type.toLowerCase()) {
			case "string":
				return '""';
			case "number":
			case "integer":
				return "0";
			case "boolean":
				return "false";
			case "array":
				return "[]";
			case "object":
				return "{}";
			default:
				return '""';
		}
	}

	/**
	 * 生成响应参数表格（不包含 Fetch 格式）
	 */
	private generateResponseParamsTable(responseParams: any[], collectNestedStructures: Function, includeTable: boolean = true): string {
		let mdContent = "";

		if (includeTable) {
			mdContent += `### ${I18n.t("markdown.response.params")}\n\n`;
			mdContent += `| ${I18n.t("markdown.table.fieldName")} | ${I18n.t("markdown.table.type")} | ${I18n.t("markdown.table.description")} |\n`;
			mdContent += `| --- | --- | --- |\n`;

			responseParams.forEach((param: any) => {
				const displayType = this.getDisplayType(param.paramName, param.paramType);
				mdContent += `| ${param.paramName} | ${displayType} | ${param.paramDesc || ""} |\n`;
				if (param.children && param.children.length > 0) {
					collectNestedStructures(param.paramName, param.children);
				}
			});

			mdContent += "\n";
		}

		return mdContent;
	}

	private generateNestedStructuresMarkdown(nestedStructures: any[]): string {
		let mdContent = "";

		nestedStructures.forEach((structure: any) => {
			// 使用优化后的结构名称
			const structureName = this.getStructureName(structure);
			const levelPrefix = "#".repeat(Math.min(4 + structure.level, 6));
			mdContent += `${levelPrefix} ${structureName}\n\n`;
			mdContent += `| ${I18n.t("markdown.table.fieldName")} | ${I18n.t("markdown.table.type")} | ${I18n.t("markdown.table.description")} |\n`;
			mdContent += `| --- | --- | --- |\n`;

			structure.children.forEach((child: any) => {
				const displayType = this.getDisplayType(child.paramName, child.paramType);
				mdContent += `| ${child.paramName} | ${displayType} | ${child.paramDesc || ""} |\n`;
			});

			mdContent += "\n";
		});

		return mdContent;
	}



	/**
	 * 获取结构名称，对 [Array Item] 进行特殊处理
	 */
	private getStructureName(structure: any): string {
		if (structure.isArrayItem) {
			// 如果是数组项结构，使用数组名称 + _item structure
			return `${this.capitalizeFirstLetter(structure.name)}_item structure`;
		} else {
			// 普通结构使用首字母大写的格式，与表格中的类型格式保持一致
			return `${this.capitalizeFirstLetter(structure.name)} Structure`;
		}
	}

	/**
	 * 生成 Fetch 请求配置 JSON
	 */
	private generateFetchRequestJson(requestParams: any[], tool: any): string {
		let mdContent = `**${I18n.t("markdown.fetch.request.title")}：**\n`;
		mdContent += "```javascript\n";

		// 构建完整的 fetch 配置
		mdContent += "{\n";
		mdContent += '  method: "POST",\n';

		// 使用实际的 pluginToolId 而不是占位符
		const pluginToolId = tool.pluginToolId || '${pluginToolId}';
		mdContent += `  url: "${getCurrentBaseUrl()}/api/saas/tool/v1/plugin/run/${pluginToolId}",\n`;

		mdContent += "  headers: {\n";
		mdContent += '    "Content-Type": "application/json",\n';
		mdContent += `    "Authorization": "${getCurrentApiKey()}"\n`;
		mdContent += "  },\n";
		mdContent += "  body: {\n";
		mdContent += "    params: {\n";

		// 生成带注释的参数，传入 isRequest = true
		mdContent += this.generateParametersWithComments(requestParams, 6, true);

		mdContent += "    }\n";
		mdContent += "  }\n";
		mdContent += "}\n";
		mdContent += "```\n\n";

		return mdContent;
	}

	/**
	 * 生成 Fetch 响应格式 JSON
	 */
	private generateFetchResponseJson(responseParams: any[], tool?: any): string {
		let mdContent = `**${I18n.t("markdown.fetch.response.title")}：**\n`;
		mdContent += "```javascript\n";

		// 构建完整的响应格式
		mdContent += "{\n";
		mdContent += "  code: 200, // number, 响应状态码\n";
		mdContent += '  msg: "", // string, 响应消息\n';
		mdContent += "  data: {\n";

		// 生成带注释的响应参数，传入 isRequest = false
		mdContent += this.generateParametersWithComments(responseParams, 4, false);

		mdContent += "  }\n";
		mdContent += "}\n";
		mdContent += "```\n\n";

		return mdContent;
	}

	/**
	 * 生成带注释的参数列表
	 */
	private generateParametersWithComments(params: any[], indentLevel: number = 4, isRequest: boolean = false): string {
		let content = "";
		const indent = " ".repeat(indentLevel);

		params.forEach((param: any, index: number) => {
			const isLast = index === params.length - 1;
			const required = isRequest ? (param.requiredFlag ? "必填" : "不必填") : "";
			const comment = isRequest
				? `// ${param.paramType}, ${required}, ${param.paramDesc || ""}`
				: `// ${param.paramType}, ${param.paramDesc || ""}`;

			// 跳过 [Array Item] 这种特殊的参数名，直接处理其子元素
			if (param.paramName === "[Array Item]" && param.children && param.children.length > 0) {
				content += this.generateParametersWithComments(param.children, indentLevel, isRequest);
				return;
			}

			if (param.paramType === "array" || param.paramType === "Array") {
				// 处理数组类型（优先检查数组类型）
				content += `${indent}${param.paramName}: [ ${comment}\n`;

				// 如果有子元素定义，生成对象数组
				if (param.children && param.children.length > 0) {
					// 检查子元素是否是 [Array Item]
					const hasArrayItem = param.children.some((child: any) => child.paramName === "[Array Item]");
					if (hasArrayItem) {
						// 如果有 [Array Item]，直接处理其子元素
						const arrayItemChild = param.children.find((child: any) => child.paramName === "[Array Item]");
						if (arrayItemChild && arrayItemChild.children && arrayItemChild.children.length > 0) {
							content += `${indent}  { // object, 数组元素\n`;
							content += this.generateParametersWithComments(arrayItemChild.children, indentLevel + 4, isRequest);
							content += `${indent}  }\n`;
						}
					} else {
						content += `${indent}  { // object, 数组元素\n`;
						content += this.generateParametersWithComments(param.children, indentLevel + 4, isRequest);
						content += `${indent}  }\n`;
					}
				} else {
					// 为基本类型数组添加示例元素
					const arrayElementType = this.getArrayElementType(param);
					const arrayElementComment = `// ${arrayElementType}, 数组元素`;
					content += `${indent}  ${this.getDefaultValueByType(arrayElementType)} ${arrayElementComment}\n`;
				}

				content += `${indent}]${isLast ? "" : ","}\n`;
			} else if (param.children && param.children.length > 0) {
				// 处理嵌套对象（非数组的对象类型）
				content += `${indent}${param.paramName}: { ${comment}\n`;
				content += this.generateParametersWithComments(param.children, indentLevel + 2, isRequest);
				content += `${indent}}${isLast ? "" : ","}\n`;
			} else {
				// 处理基本类型
				const value = this.getDefaultValueByType(param.paramType);
				content += `${indent}${param.paramName}: ${value}${isLast ? "" : ","} ${comment}\n`;
			}
		});

		return content;
	}

	/**
	 * 获取数组元素类型
	 */
	private getArrayElementType(param: any): string {
		// 如果有子元素定义，使用第一个子元素的类型
		if (param.children && param.children.length > 0) {
			return param.children[0].paramType || "string";
		}
		// 默认返回 string 类型
		return "string";
	}

	/**
	 * 获取表格中显示的类型格式
	 * Object 类型：显示为字段名首字母大写，如 data → Data
	 * Array 类型：显示为字段名首字母大写 + _item[]，如 sub_task_result_list → Sub_task_result_list_item[]
	 * 其他类型：保持原样
	 */
	private getDisplayType(paramName: string, paramType: string): string {
		const normalizedType = paramType.toLowerCase();

		if (normalizedType === "object") {
			// Object 类型：首字母大写
			return this.capitalizeFirstLetter(paramName);
		} else if (normalizedType === "array") {
			// Array 类型：首字母大写 + _item[]
			return this.capitalizeFirstLetter(paramName) + "_item[]";
		} else {
			// 其他类型保持原样
			return paramType;
		}
	}

	/**
	 * 将字符串首字母大写
	 */
	private capitalizeFirstLetter(str: string): string {
		if (!str) return str;
		return str.charAt(0).toUpperCase() + str.slice(1);
	}
}
