import * as vscode from 'vscode';
import { Logger } from '../utils/logger';
import { JoycodeHelper } from '../utils/joycodeHelper';

export class MarkdownProvider implements vscode.TextDocumentContentProvider {
	private _content: string = '';
	private _currentUri: vscode.Uri | null = null;
	private _onDidChangeEmitter = new vscode.EventEmitter<vscode.Uri>();
	public readonly onDidChange = this._onDidChangeEmitter.event;

	public setContent(content: string, uri?: vscode.Uri) {
		Logger.info(`[MarkdownProvider] 设置内容，长度: ${content.length}, URI: ${uri?.toString() || 'undefined'}`);
		this._content = content;
		if (uri) {
			this._currentUri = uri;
		}
		// 通知内容已更改，使用传入的URI或当前URI
		const targetUri = uri || this._currentUri || vscode.Uri.parse('plugin-detail:current.md');
		Logger.info(`[MarkdownProvider] 触发内容变更事件，URI: ${targetUri.toString()}`);

		// 使用setTimeout确保内容变更事件在下一个事件循环中触发
		setTimeout(() => {
			this._onDidChangeEmitter.fire(targetUri);
			Logger.info(`[MarkdownProvider] 内容变更事件已触发`);
		}, 0);
	}

	public provideTextDocumentContent(uri: vscode.Uri): string {
		Logger.info(`[MarkdownProvider] 提供文档内容，URI: ${uri.toString()}, 内容长度: ${this._content.length}`);
		return this._content;
	}

	dispose() {
		this._onDidChangeEmitter.dispose();
	}
}
