import * as vscode from "vscode";
import { getUserKeyList } from "../service/api";
import { setCurrentApiKeyToCache, setPtKeyToCache, setApiKeyListToCache, clearAuthCache, isAuthReady, getCurrentApiKey } from "../config";
import { EnvironmentHelper, updateGlobalConfig, setLoginContext, GlobalConfigContext } from "./globalContext";
import { Logger } from "./logger";
import { selectData } from "../types";
import { maskKey } from "./mask";
import { AI_RESOURCES_CLOSE_SETTINGS } from "../constants/commands";

/**
 * 认证信息初始化管理器
 * 在扩展激活时统一处理所有认证信息，放入内存缓存
 */
export class AuthInitializer {
	private static instance: AuthInitializer;
	private isInitialized = false;

	private constructor() { }

	public static getInstance(): AuthInitializer {
		if (!AuthInitializer.instance) {
			AuthInitializer.instance = new AuthInitializer();
		}
		return AuthInitializer.instance;
	}

	/**
	 * 扩展激活时的初始化逻辑
	 */
	public async initialize(): Promise<{ isLoggedIn: boolean; isReady: boolean }> {
		try {
			Logger.info("[AuthInitializer] 开始扩展激活初始化...");

			// 清空之前的缓存
			clearAuthCache();

			// 1. 检查是否在 JoyCode 环境
			const isJoyCode = this.isJoyCodeEnvironment();
			if (!isJoyCode) {
				Logger.warn("[AuthInitializer] 不在 JoyCode 环境中");
				setLoginContext(false);
				return { isLoggedIn: false, isReady: false };
			}

			// 2. 检查用户登录状态
			const userInfo = await this.getUserInfo();
			if (!userInfo || !userInfo.pt_key) {
				Logger.info("[AuthInitializer] 用户未登录，清空全局配置，显示登录页面，不展示设置按钮");
				updateGlobalConfig(null);
				setLoginContext(false);
				return { isLoggedIn: false, isReady: false };
			}

			Logger.info(`[AuthInitializer] 用户已登录: ${JSON.stringify(userInfo)}`);

			// 更新全局配置
			updateGlobalConfig(userInfo);

			// 3. 执行用户登录逻辑
			const loginResult = await this.handleUserLogin(userInfo);

			return { isLoggedIn: true, isReady: loginResult };

		} catch (error) {
			Logger.error(`[AuthInitializer] 扩展激活初始化失败: ${error instanceof Error ? error.message : String(error)}`);
			clearAuthCache();
			setLoginContext(false);
			return { isLoggedIn: false, isReady: false };
		}
	}

	/**
	 * 处理用户登录逻辑
	 */
	private async handleUserLogin(userInfo: any): Promise<boolean> {
		try {
			Logger.info("[AuthInitializer] 开始处理用户登录逻辑...");

			// 2.1 获取用户信息（已在上层获取）
			await setPtKeyToCache(userInfo.pt_key);
			Logger.info("[AuthInitializer] 2.1 用户信息已处理，PT Key 已设置");

			// 2.2 获取 API Key
			const apiKey = await this.getApiKey();
			if (!apiKey) {
				Logger.warn("[AuthInitializer] 2.2 获取 API Key 失败");
				setLoginContext(true); // 用户已登录，但没有 API Key
				return false;
			}

			await setCurrentApiKeyToCache(apiKey);
			Logger.info("[AuthInitializer] 2.2 API Key 已设置到内存缓存");

			// 注释：激活时不再自动写入 apikey 文件，只有用户主动保存时才写入
			// await this.ensureApiKeyFileOnActivation(apiKey, userInfo);

			// 2.3 获取列表（这里暂时不获取，等界面加载时再获取）
			Logger.info("[AuthInitializer] 2.3 列表获取将在界面加载时进行");

			// 设置登录状态
			setLoginContext(true);

			// 验证认证信息是否完整
			const isReady = isAuthReady();
			Logger.info(`[AuthInitializer] 用户登录逻辑处理完成，状态: ${isReady ? '就绪' : '未就绪'}`);

			this.isInitialized = true;
			return isReady;

		} catch (error) {
			Logger.error(`[AuthInitializer] 用户登录逻辑处理失败: ${error instanceof Error ? error.message : String(error)}`);
			setLoginContext(false);
			return false;
		}
	}

	/**
	 * 2.2 获取 API Key（纯内存级别缓存）
	 */
	private async getApiKey(): Promise<string | undefined> {
		Logger.info("[AuthInitializer] 开始获取 API Key（仅使用内存缓存）...");

		// 1. 先检查内存缓存中是否已有 API Key
		const cachedApiKey = getCurrentApiKey();
		if (cachedApiKey) {
			Logger.info("[AuthInitializer] 从内存缓存中获取到 API Key");
			return cachedApiKey;
		}

		// 2. 内存中没有，从接口获取
		Logger.info("[AuthInitializer] 内存中没有 API Key，从接口获取");
		const apiKeyList = await this.fetchApiKeyList();
		if (!apiKeyList || apiKeyList.length === 0) {
			Logger.warn("[AuthInitializer] 接口返回的 API Key 列表为空");
			return undefined;
		}

		// 设置 API Key 列表到内存缓存
		setApiKeyListToCache(apiKeyList);
		Logger.info(`[AuthInitializer] API Key 列表已设置到内存缓存: ${apiKeyList.length} 个`);

		// 使用第一个 API Key
		const apiKey = String(apiKeyList[0].value);
		Logger.info("[AuthInitializer] 使用第一个 API Key");

		return apiKey;
	}

	/**
	 * 3. 监听用户登录状态变化
	 */
	public async handleLoginStatusChange(params: any): Promise<{ isLoggedIn: boolean; isReady: boolean }> {
		Logger.info(`[AuthInitializer] 3. 监听到登录状态变化: ${JSON.stringify(params)}`);

		if (params?.userInfo?.pt_key) {
			Logger.info("[AuthInitializer] 用户登录，更新全局配置并重新执行登录逻辑");

			// 更新全局配置上下文
			updateGlobalConfig(params.userInfo);

			const loginResult = await this.handleUserLogin(params.userInfo);
			return { isLoggedIn: true, isReady: loginResult };
		} else {
			Logger.info("[AuthInitializer] 用户退出，清空认证信息和全局配置，显示登录页面");

			// 关闭设置面板
			try {
				await vscode.commands.executeCommand(AI_RESOURCES_CLOSE_SETTINGS);
				Logger.info("[AuthInitializer] 已关闭设置面板");
			} catch (error) {
				Logger.warn(`[AuthInitializer] 关闭设置面板失败: ${error instanceof Error ? error.message : String(error)}`);
			}

			// 清空全局配置
			updateGlobalConfig(null);

			// 清空内存中的所有认证信息（API Key、PT Key、API Key 列表）
			Logger.info("[AuthInitializer] 清空内存中的认证信息缓存");
			clearAuthCache();

			setLoginContext(false);
			this.isInitialized = false;
			return { isLoggedIn: false, isReady: false };
		}
	}

	/**
	 * 检查是否已初始化
	 */
	public isReady(): boolean {
		return this.isInitialized && isAuthReady();
	}

	/**
	 * 检查是否在 JoyCode 环境
	 */
	private isJoyCodeEnvironment(): boolean {
		return EnvironmentHelper.isJoyCodeEnvironment();
	}

	/**
	 * 获取用户信息
	 * 优先从全局配置中获取，如果没有则重新获取
	 */
	private async getUserInfo(): Promise<any> {
		try {
			// 首先尝试从全局配置中获取用户信息（避免重复调用）
			const globalConfig = GlobalConfigContext.getInstance();
			const cachedUserInfo = globalConfig.getUserInfo();

			if (cachedUserInfo && cachedUserInfo.pt_key) {
				Logger.info("[AuthInitializer] 从全局配置中获取用户信息");
				return cachedUserInfo;
			}

			// 如果全局配置中没有，则重新获取
			Logger.info("[AuthInitializer] 全局配置中无用户信息，重新获取");
			const userInfo = await EnvironmentHelper.getLoginUserInfo();
			return userInfo ? { pt_key: userInfo.pt_key, ...userInfo } : null;
		} catch (error) {
			Logger.error(`[AuthInitializer] 获取用户信息失败: ${error instanceof Error ? error.message : String(error)}`);
			return null;
		}
	}







	/**
	 * 获取 API Key 列表
	 */
	private async fetchApiKeyList(): Promise<selectData[]> {
		try {
			Logger.info("[AuthInitializer] 开始获取 API Key 列表...");
			const res = await getUserKeyList();

			let keyList: selectData[] = [];
			if (res && res.data && Array.isArray(res.data.data)) {
				keyList = res.data.data.map((item: any) => ({
					value: item.apiKey,
					label: maskKey(item.apiKey),
				}));
			}

			Logger.info(`[AuthInitializer] 获取到 ${keyList.length} 个 API Key`);
			return keyList;
		} catch (error) {
			Logger.error(`[AuthInitializer] 获取 API Key 列表失败: ${error instanceof Error ? error.message : String(error)}`);
			return [];
		}
	}
}
