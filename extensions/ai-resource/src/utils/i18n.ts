import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 国际化工具类
 */
export class I18n {
    private static packageNls: any = null;

    /**
     * 初始化国际化
     */
    static async init(context: vscode.ExtensionContext) {
        try {
            // 尝试加载对应语言的国际化文件
            const locale = vscode.env.language;
            let nlsFileName: string;

            if (locale === 'zh-cn' || locale === 'zh-CN') {
                nlsFileName = 'package.nls.zh-cn.json';
            } else {
                nlsFileName = 'package.nls.json';
            }

            const nlsPath = path.join(context.extensionPath, nlsFileName);

            // 使用 fs 读取文件内容
            const content = fs.readFileSync(nlsPath, 'utf8');
            this.packageNls = JSON.parse(content);
        } catch (error) {
            console.warn('Failed to load i18n file, using fallback:', error);
            // 如果加载失败，使用默认的英文
            this.packageNls = {};
        }
    }

    /**
     * 获取本地化字符串
     * @param key 国际化键
     * @param args 格式化参数
     * @returns 本地化字符串
     */
    static t(key: string, ...args: any[]): string {
        if (!this.packageNls) {
            return key; // 如果没有初始化，返回键本身
        }

        let message = this.packageNls[key] || key;
        
        // 处理参数替换 {0}, {1}, {2} 等
        if (args.length > 0) {
            args.forEach((arg, index) => {
                message = message.replace(new RegExp(`\\{${index}\\}`, 'g'), String(arg));
            });
        }
        
        return message;
    }

    /**
     * 获取当前语言环境
     */
    static getLocale(): string {
        return vscode.env.language;
    }

    /**
     * 是否为中文环境
     */
    static isChinese(): boolean {
        const locale = this.getLocale();
        return locale === 'zh-cn' || locale === 'zh-CN';
    }
}
