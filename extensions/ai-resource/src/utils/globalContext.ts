/**
 * 全局配置上下文和环境检测工具
 * 统一管理环境检测、配置管理和用户信息获取
 */

import * as vscode from 'vscode';
import { Logger } from './logger';
import { I18n } from './i18n';
import { JOYCODE_GET_LOGIN_INFO } from '../constants/commands';

/**
 * 内部用户固定的 baseUrl
 */
export const INTERNAL_USER_BASE_URL = 'https://joycode-api.jd.com';

/**
 * 内部用户插件服务的域名
 */
export const INTERNAL_USER_PLUGIN_BASE_URL = 'https://joyagent.jd.com';

/**
 * 配置信息接口
 */
export interface JoyCodeConfig {
    env: string;
    baseUrl: string;
    apiUrl: string;
}

/**
 * 环境信息接口
 */
export interface EnvironmentInfo {
    uriScheme: string;
    isJoyCode: boolean;
    isRemote: boolean;
    remoteName?: string;
}

/**
 * 环境检测工具类
 * 统一管理所有与 vscode.env.uriScheme 相关的环境检测逻辑
 */
export class EnvironmentHelper {
    /**
     * JoyCode 环境的 URI Scheme
     */
    private static readonly JOYCODE_URI_SCHEME = 'joycode';

    /**
     * 检查当前是否在 JoyCode 环境中
     * @returns {boolean} 如果在 JoyCode 环境中返回 true，否则返回 false
     */
    static isJoyCodeEnvironment(): boolean {
        const uriScheme = vscode.env.uriScheme;
        const isJoyCode = uriScheme === this.JOYCODE_URI_SCHEME;

        Logger.info(`[EnvironmentHelper] 环境检测 - uriScheme: ${uriScheme}, isJoyCode: ${isJoyCode}`);

        return isJoyCode;
    }

    /**
     * 获取当前的 URI Scheme
     * @returns {string} 当前的 URI Scheme
     */
    static getCurrentUriScheme(): string {
        return vscode.env.uriScheme;
    }

    /**
     * 检查是否在远程环境中
     * @returns {boolean} 如果在远程环境中返回 true，否则返回 false
     */
    static isRemoteEnvironment(): boolean {
        return !!vscode.env.remoteName;
    }

    /**
     * 获取完整的环境信息
     * @returns {EnvironmentInfo} 包含环境信息的对象
     */
    static getEnvironmentInfo(): EnvironmentInfo {
        const uriScheme = vscode.env.uriScheme;
        const isJoyCode = this.isJoyCodeEnvironment();
        const isRemote = this.isRemoteEnvironment();
        const remoteName = vscode.env.remoteName;

        const envInfo = {
            uriScheme,
            isJoyCode,
            isRemote,
            ...(remoteName && { remoteName })
        };

        Logger.info(`[EnvironmentHelper] 环境信息: ${JSON.stringify(envInfo)}`);

        return envInfo;
    }

    /**
     * 验证 JoyCode 环境并显示错误信息（如果不在 JoyCode 环境中）
     * @param showErrorMessage {boolean} 是否显示错误消息，默认为 true
     * @returns {boolean} 如果在 JoyCode 环境中返回 true，否则返回 false
     */
    static validateJoyCodeEnvironment(showErrorMessage: boolean = true): boolean {
        const isJoyCode = this.isJoyCodeEnvironment();

        if (!isJoyCode) {
            Logger.error("[EnvironmentHelper] 非法情况！请在 JoyCode 软件中使用该插件！");

            if (showErrorMessage) {
                vscode.window.showInformationMessage(I18n.t("environment.error.useInJoyCode"));
            }
        }

        return isJoyCode;
    }

    /**
     * 记录环境信息到日志
     */
    static logEnvironmentInfo(): void {
        const envInfo = this.getEnvironmentInfo();
        Logger.info(`[EnvironmentHelper] 当前环境信息:`);
        Logger.info(`  - URI Scheme: ${envInfo.uriScheme}`);
        Logger.info(`  - JoyCode 环境: ${envInfo.isJoyCode}`);
        Logger.info(`  - 远程环境: ${envInfo.isRemote}`);
        if (envInfo.remoteName) {
            Logger.info(`  - 远程名称: ${envInfo.remoteName}`);
        }
    }

    /**
     * 异步获取登录用户信息（仅在需要时使用）
     * @returns Promise<any | null> 用户信息或 null
     */
    static async getLoginUserInfo(): Promise<any | null> {
        try {
            Logger.info('[EnvironmentHelper.getLoginUserInfo] 开始获取用户信息...');
            const loginInfo = await vscode.commands.executeCommand(JOYCODE_GET_LOGIN_INFO);
            Logger.info(`[EnvironmentHelper.getLoginUserInfo] 获取到的登录信息: ${JSON.stringify(loginInfo)}`);

            if (loginInfo && typeof loginInfo === 'object') {
                // JOYCODE_GET_LOGIN_INFO 直接返回用户信息对象，不是包装格式
                const userInfo = loginInfo as any;

                // 检查是否有必要的字段来判断用户是否已登录
                if (userInfo.pt_key && userInfo.userName) {
                    Logger.info(`[EnvironmentHelper.getLoginUserInfo] 用户已登录，返回用户信息`);
                    return userInfo;
                } else {
                    Logger.warn(`[EnvironmentHelper.getLoginUserInfo] 用户信息不完整，pt_key: ${!!userInfo.pt_key}, userName: ${!!userInfo.userName}`);
                    return null;
                }
            }
            Logger.warn('[EnvironmentHelper.getLoginUserInfo] 登录信息格式不正确或为空');
            return null;
        } catch (error) {
            Logger.error(`[EnvironmentHelper.getLoginUserInfo] 获取用户信息失败: ${error instanceof Error ? error.message : String(error)}`);
            return null;
        }
    }
}

/**
 * 全局配置上下文类
 */
export class GlobalConfigContext {
    private static instance: GlobalConfigContext;
    private config: JoyCodeConfig;
    private userInfo: any = null; // 存储用户信息

    private constructor() {
        // 初始化默认配置
        this.config = this.getDefaultConfig();
    }

    public static getInstance(): GlobalConfigContext {
        if (!GlobalConfigContext.instance) {
            GlobalConfigContext.instance = new GlobalConfigContext();
        }
        return GlobalConfigContext.instance;
    }

    /**
     * 获取默认配置（从 vscode.env）
     */
    private getDefaultConfig(): JoyCodeConfig {
        const {
            joyCodeBaseUrl = INTERNAL_USER_BASE_URL,
            joyCodeEnv = "prod",
        } = vscode.env as any;

        return {
            env: joyCodeEnv,
            baseUrl: joyCodeBaseUrl,
            apiUrl: joyCodeBaseUrl, // apiUrl 和 baseUrl 是同一个地址
        };
    }

    /**
     * 初始化配置上下文
     * 在插件激活时调用
     */
    public async initialize(): Promise<void> {
        Logger.info('[GlobalConfigContext] 初始化配置上下文...');

        // 先设置默认配置
        this.config = this.getDefaultConfig();
        Logger.info(`[GlobalConfigContext] 默认配置: ${JSON.stringify(this.config)}`);

        // 尝试获取用户登录信息并更新配置
        await this.updateFromUserInfo();
    }

    /**
     * 从用户信息更新配置
     */
    private async updateFromUserInfo(): Promise<void> {
        try {
            const userInfo = await EnvironmentHelper.getLoginUserInfo();
            if (userInfo) {
                this.userInfo = userInfo; // 存储用户信息
                this.updateConfig(userInfo);
            } else {
                this.userInfo = null; // 清空用户信息
                Logger.info('[GlobalConfigContext] 用户未登录，使用默认配置');
            }
        } catch (error) {
            this.userInfo = null; // 出错时清空用户信息
            Logger.warn(`[GlobalConfigContext] 获取用户信息失败，使用默认配置: ${error instanceof Error ? error.message : String(error)}`);
        }
    }

    /**
     * 更新配置（在登录状态变化时调用）
     */
    public updateConfig(userInfo: any): void {
        // 更新用户信息
        this.userInfo = userInfo;

        const defaultConfig = this.getDefaultConfig();

        let baseUrl = defaultConfig.baseUrl;
        let env = defaultConfig.env;
        Logger.info(` 用户信息', ${JSON.stringify(this.userInfo)}`);


        // 如果有用户信息，优先使用用户配置
        if (userInfo) {
            // 通过用户信息的 tenant 字段来判断是否为内部用户
            if (userInfo.tenant == 'JD' || userInfo.orgName == '京东集团') {
                env = 'inner';
            }
            baseUrl = INTERNAL_USER_BASE_URL;

            // // 判断是否为内部用户
            // const isInternalUser = env === 'inner';

            // if (isInternalUser) {
            //     // 内部用户固定使用 joycoder-api.jd.com
            //     baseUrl = INTERNAL_USER_BASE_URL;
            //     Logger.info(`[GlobalConfigContext] 内部用户，使用固定baseUrl: ${baseUrl}`);
            // } else {
            //     // 外部用户使用用户配置或默认配置
            //     if (userInfo.base_url && typeof userInfo.base_url === 'string' && userInfo.base_url.trim() !== '') {
            //         baseUrl = userInfo.base_url;
            //     }
            //     Logger.info(`[GlobalConfigContext] 外部用户，使用配置baseUrl: ${baseUrl}`);
            // }

            Logger.info(`[GlobalConfigContext] 使用用户配置: baseUrl=${baseUrl}, env=${env}`);
        } else {
            Logger.info(`[GlobalConfigContext] 清空用户配置，使用默认配置`);
        }

        this.config = {
            env,
            baseUrl,
            apiUrl: baseUrl, // apiUrl 和 baseUrl 是同一个地址
        };

        Logger.info(`[GlobalConfigContext] 配置已更新: ${JSON.stringify(this.config)}`);
    }

    /**
     * 获取当前配置
     */
    public getConfig(): JoyCodeConfig {
        return { ...this.config }; // 返回副本，防止外部修改
    }

    /**
     * 获取用户信息
     */
    public getUserInfo(): any {
        return this.userInfo;
    }

    /**
     * 获取 baseUrl
     */
    public getBaseUrl(): string {
        return this.config.baseUrl;
    }

    /**
     * 获取 apiUrl
     */
    public getApiUrl(): string {
        return this.config.apiUrl;
    }

    /**
     * 获取环境
     */
    public getEnv(): string {
        return this.config.env;
    }
}

// 导出单例实例
export const globalConfigContext = GlobalConfigContext.getInstance();

// 导出便捷方法
export function getJoyCodeConfig(): JoyCodeConfig {
    return globalConfigContext.getConfig();
}

export function getCurrentBaseUrl(): string {
    return globalConfigContext.getBaseUrl();
}

export function getCurrentApiUrl(): string {
    return globalConfigContext.getApiUrl();
}

export function getCurrentEnv(): string {
    return globalConfigContext.getEnv();
}

/**
 * 初始化全局配置上下文
 * 在插件激活时调用
 */
export async function initializeGlobalConfig(): Promise<void> {
    await globalConfigContext.initialize();
}

/**
 * 更新全局配置
 * 在登录状态变化时调用
 */
export function updateGlobalConfig(userInfo: any): void {
    globalConfigContext.updateConfig(userInfo);
}

/**
 * 设置登录上下文状态
 * 用于控制 VSCode 命令和 UI 的显示
 */
export function setLoginContext(isLoggedIn: boolean): void {
    vscode.commands.executeCommand(
        "setContext",
        "aiResources.isLoggedIn",
        isLoggedIn,
    );
}
