import * as vscode from "vscode";
import * as path from "path";
import { Logger } from "./logger";
import { I18n } from "./i18n";

export class JoycodeHelper {
	/**
	 * AI 资源目录名常量
	 */
	private static readonly AI_RESOURCES_DIR_NAME = 'resources';

	/**
	 * 获取 joycode 目录名配置
	 */
	static getJoycodeDirName(): string {
		const config = vscode.workspace.getConfiguration('resource');
		return config.get('joycodeDir', '.joycode');
	}

	/**
	 * 获取 API key 文件名配置
	 */
	static getApiKeyFileName(): string {
		const config = vscode.workspace.getConfiguration('resource');
		return config.get('apiKeyFile', 'apikey.json');
	}

	/**
	 * 获取工作空间根URI
	 */
	static getWorkspaceRootUri(): vscode.Uri | undefined {
		const folders = vscode.workspace.workspaceFolders;
		// Logger.info('[JoycodeHelper] workspaceFolders: ' + JSON.stringify(folders));
		if (!folders || folders.length === 0) return undefined;
		return folders[0].uri;
	}

	/**
	 * 获取.joycode目录的URI
	 */
	static getJoycodeDirUri(): vscode.Uri | undefined {
		const rootUri = this.getWorkspaceRootUri();
		if (!rootUri) return undefined;
		const joycodeDirUri = vscode.Uri.joinPath(rootUri, this.getJoycodeDirName());
		// Logger.info('[JoycodeHelper] getJoycodeDirUri: ' + joycodeDirUri.toString());
		return joycodeDirUri;
	}

	/**
	 * 获取 AI 资源目录的 URI (.joycode/resources)
	 */
	static getAiResourceDirUri(): vscode.Uri | undefined {
		const workspaceUri = this.getWorkspaceRootUri();
		if (!workspaceUri) return undefined;
		return vscode.Uri.joinPath(
			workspaceUri,
			this.getJoycodeDirName(),
			this.AI_RESOURCES_DIR_NAME,
		);
	}

	/**
	 * 查找当前项目根目录下的.joycode文件夹路径（向后兼容）
	 */
	static getJoycodeDir(): string | undefined {
		const rootUri = this.getWorkspaceRootUri();
		if (!rootUri) return undefined;
		const rootPath = rootUri.fsPath;
		const joycodeDir = path.join(rootPath, this.getJoycodeDirName());
		// Logger.info("[JoycodeHelper] getJoycodeDir: " + joycodeDir);
		return joycodeDir;
	}

	/**
	 * 获取apikey.json文件的完整URI（新位置：.joycode/resources/apikey.json）
	 */
	static getApiKeyFileUri(): vscode.Uri | undefined {
		const aiResourceDirUri = this.getAiResourceDirUri();
		if (!aiResourceDirUri) return undefined;
		return vscode.Uri.joinPath(aiResourceDirUri, this.getApiKeyFileName());
	}

	/**
	 * 检查文件/文件夹是否存在（异步）
	 */
	static async exists(uri: vscode.Uri): Promise<boolean> {
		try {
			await vscode.workspace.fs.stat(uri);
			// Logger.info("[JoycodeHelper] exists: " + uri.fsPath + " true");
			return true;
		} catch (e) {
			// Logger.info("[JoycodeHelper] exists: " + uri.fsPath + " false " + e);
			return false;
		}
	}

	/**
	 * 创建.joycode/resources文件夹和apikey.json文件（如不存在）
	 */
	static async ensureApiKeyFile(apikey: string = "", baseUrl?: string) {
		const aiResourceDirUri = this.getAiResourceDirUri();
		if (!aiResourceDirUri) return;
		try {
			if (!(await this.exists(aiResourceDirUri))) {
				await vscode.workspace.fs.createDirectory(aiResourceDirUri);
			}
			const fileUri = this.getApiKeyFileUri();
			if (fileUri && !(await this.exists(fileUri))) {
				const configData: any = { apikey };
				if (baseUrl) {
					configData.baseUrl = baseUrl;
				}
				const content = Buffer.from(
					JSON.stringify(configData, null, 2),
					"utf-8",
				);
				await vscode.workspace.fs.writeFile(fileUri, content);
			}
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : String(error);
			Logger.error("[JoycodeHelper] ensureApiKeyFile 失败: " + errorMsg);
			if (errorMsg.includes("EROFS") || errorMsg.includes("read-only") || errorMsg.includes("EACCES") || errorMsg.includes("permission")) {
				this.showManualSetupGuide();
			} else {
				vscode.window.showErrorMessage(I18n.t("joycode.error.createConfigFailed", errorMsg));
			}
		}
	}



	/**
	 * 读取apikey.json内容
	 */
	static async readApiKey(): Promise<string | undefined> {
		const fileUri = this.getApiKeyFileUri();
		if (!fileUri) return undefined;
		if (!(await this.exists(fileUri))) return undefined;
		try {
			const content = await vscode.workspace.fs.readFile(fileUri);
			const json = JSON.parse(Buffer.from(content).toString("utf-8"));
			return json.apikey;
		} catch (e) {
			Logger.error("[JoycodeHelper] readApiKey - error: " + e);
			return undefined;
		}
	}

	/**
	 * 读取apikey.json中的baseUrl
	 */
	static async readBaseUrl(): Promise<string | undefined> {
		const fileUri = this.getApiKeyFileUri();
		if (!fileUri) return undefined;
		if (!(await this.exists(fileUri))) return undefined;
		try {
			const content = await vscode.workspace.fs.readFile(fileUri);
			const json = JSON.parse(Buffer.from(content).toString("utf-8"));
			return json.baseUrl;
		} catch (e) {
			Logger.error("[JoycodeHelper] readBaseUrl - error: " + e);
			return undefined;
		}
	}

	/**
	 * 读取apikey.json的完整配置
	 */
	static async readConfig(): Promise<{ apikey?: string; baseUrl?: string } | undefined> {
		const fileUri = this.getApiKeyFileUri();
		if (!fileUri) return undefined;
		if (!(await this.exists(fileUri))) return undefined;
		try {
			const content = await vscode.workspace.fs.readFile(fileUri);
			const json = JSON.parse(Buffer.from(content).toString("utf-8"));
			return {
				apikey: json.apikey,
				baseUrl: json.baseUrl
			};
		} catch (e) {
			Logger.error("[JoycodeHelper] readConfig - error: " + e);
			return undefined;
		}
	}

	/**
	 * 动态修改apikey.json中的apikey
	 */
	static async updateApiKey(newKey: string, baseUrl?: string) {
		const fileUri = this.getApiKeyFileUri();
		if (!fileUri) return;
		try {
			const aiResourceDirUri = this.getAiResourceDirUri();
			if (aiResourceDirUri && !(await this.exists(aiResourceDirUri))) {
				await vscode.workspace.fs.createDirectory(aiResourceDirUri);
			}
			let json: any = { apikey: newKey };
			if (await this.exists(fileUri)) {
				try {
					const content = await vscode.workspace.fs.readFile(fileUri);
					json = {
						...JSON.parse(Buffer.from(content).toString("utf-8")),
						apikey: newKey,
					};
				} catch (e) {
					Logger.error("[JoycodeHelper] updateApiKey - read error: " + e);
				}
			}
			// 如果提供了 baseUrl，则更新或添加它
			if (baseUrl !== undefined) {
				if (baseUrl) {
					json.baseUrl = baseUrl;
				} else {
					// 如果 baseUrl 为空字符串，则删除该字段
					delete json.baseUrl;
				}
			}
			const buffer = Buffer.from(JSON.stringify(json, null, 2), "utf-8");
			await vscode.workspace.fs.writeFile(fileUri, buffer);
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : String(error);
			Logger.error("[JoycodeHelper] updateApiKey 失败: " + errorMsg);
			if (errorMsg.includes("EROFS") || errorMsg.includes("read-only")) {
				vscode.window.showWarningMessage(
					I18n.t("joycode.error.readOnlyFileSystem"),
				);
			} else if (
				errorMsg.includes("EACCES") ||
				errorMsg.includes("permission")
			) {
				vscode.window.showWarningMessage(
					I18n.t("joycode.error.permissionDenied"),
				);
			}
		}
	}

	/**
	 * 更新apikey.json中的baseUrl
	 */
	static async updateBaseUrl(baseUrl: string) {
		const fileUri = this.getApiKeyFileUri();
		if (!fileUri) return;
		try {
			const aiResourceDirUri = this.getAiResourceDirUri();
			if (aiResourceDirUri && !(await this.exists(aiResourceDirUri))) {
				await vscode.workspace.fs.createDirectory(aiResourceDirUri);
			}
			let json: any = {};
			if (await this.exists(fileUri)) {
				try {
					const content = await vscode.workspace.fs.readFile(fileUri);
					json = JSON.parse(Buffer.from(content).toString("utf-8"));
				} catch (e) {
					Logger.error("[JoycodeHelper] updateBaseUrl - read error: " + e);
				}
			}
			// 更新 baseUrl
			if (baseUrl) {
				json.baseUrl = baseUrl;
			} else {
				// 如果 baseUrl 为空字符串，则删除该字段
				delete json.baseUrl;
			}
			const buffer = Buffer.from(JSON.stringify(json, null, 2), "utf-8");
			await vscode.workspace.fs.writeFile(fileUri, buffer);
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : String(error);
			Logger.error("[JoycodeHelper] updateBaseUrl 失败: " + errorMsg);
			if (errorMsg.includes("EROFS") || errorMsg.includes("read-only")) {
				vscode.window.showWarningMessage(
					I18n.t("joycode.error.readOnlyFileSystem"),
				);
			} else if (
				errorMsg.includes("EACCES") ||
				errorMsg.includes("permission")
			) {
				vscode.window.showWarningMessage(
					I18n.t("joycode.error.permissionDenied"),
				);
			}
		}
	}

	/**
	 * 更新apikey.json的完整配置
	 */
	static async updateConfig(config: { apikey?: string; baseUrl?: string }) {
		const fileUri = this.getApiKeyFileUri();
		if (!fileUri) return;
		try {
			const aiResourceDirUri = this.getAiResourceDirUri();
			if (aiResourceDirUri && !(await this.exists(aiResourceDirUri))) {
				await vscode.workspace.fs.createDirectory(aiResourceDirUri);
			}
			let json: any = {};
			if (await this.exists(fileUri)) {
				try {
					const content = await vscode.workspace.fs.readFile(fileUri);
					json = JSON.parse(Buffer.from(content).toString("utf-8"));
				} catch (e) {
					Logger.error("[JoycodeHelper] updateConfig - read error: " + e);
				}
			}
			// 更新配置
			if (config.apikey !== undefined) {
				json.apikey = config.apikey;
			}
			if (config.baseUrl !== undefined) {
				if (config.baseUrl) {
					json.baseUrl = config.baseUrl;
				} else {
					// 如果 baseUrl 为空字符串，则删除该字段
					delete json.baseUrl;
				}
			}
			const buffer = Buffer.from(JSON.stringify(json, null, 2), "utf-8");
			await vscode.workspace.fs.writeFile(fileUri, buffer);
		} catch (error) {
			const errorMsg = error instanceof Error ? error.message : String(error);
			Logger.error("[JoycodeHelper] updateConfig 失败: " + errorMsg);
			if (errorMsg.includes("EROFS") || errorMsg.includes("read-only")) {
				vscode.window.showWarningMessage(
					I18n.t("joycode.error.readOnlyFileSystem"),
				);
			} else if (
				errorMsg.includes("EACCES") ||
				errorMsg.includes("permission")
			) {
				vscode.window.showWarningMessage(
					I18n.t("joycode.error.permissionDenied"),
				);
			}
		}
	}

	/**
	 * 显示手动创建配置文件的指导
	 */
	static showManualSetupGuide() {
		const aiResourceDir = this.getAiResourceDirUri();
		if (!aiResourceDir) {
			vscode.window.showErrorMessage(I18n.t("joycode.error.noWorkspace"));
			return;
		}

		// 获取动态配置的目录名和文件名
		const joycodeDir = this.getJoycodeDirName();
		const apiKeyFileName = this.getApiKeyFileName();
		const message = I18n.t("joycode.guide.manualSetup", aiResourceDir.fsPath, joycodeDir, apiKeyFileName);

		vscode.window
			.showInformationMessage(
				I18n.t("joycode.guide.title"),
				I18n.t("joycode.guide.viewSteps"),
				I18n.t("joycode.guide.openDirectory"),
			)
			.then((selection) => {
				if (selection === I18n.t("joycode.guide.viewSteps")) {
					vscode.window.showInformationMessage(message);
				} else if (selection === I18n.t("joycode.guide.openDirectory")) {
					vscode.env.openExternal(aiResourceDir);
				}
			});
	}

	/**
	 * 保存 Markdown 文件到 .joycode/resources 目录下
	 */
	static async saveMarkdownToAiResourceDir(
		filename: string,
		content: string,
	): Promise<vscode.Uri | undefined> {
		const aiResourceDirUri = this.getAiResourceDirUri();
		if (!aiResourceDirUri) {
			vscode.window.showErrorMessage(I18n.t("joycode.error.workspaceNotFound"));
			return;
		}
		// 确保目录存在
		await vscode.workspace.fs.createDirectory(aiResourceDirUri);
		// 拼接文件名
		const fileUri = vscode.Uri.joinPath(aiResourceDirUri, filename);
		await vscode.workspace.fs.writeFile(fileUri, Buffer.from(content, "utf8"));
		return fileUri;
	}
}
