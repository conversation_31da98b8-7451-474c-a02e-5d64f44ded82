/*--------------------------------------------------------------------------------------
 *  Copyright 2025 Glass Devtools, Inc. All rights reserved.
 *  Licensed under the Apache License, Version 2.0. See LICENSE.txt for more information.
 *--------------------------------------------------------------------------------------*/

import * as vscode from "vscode";
import { Logger } from "./logger";

/**
 * AI Resource 扩展统计助手
 *
 * 用于追踪用户在 AI Resource 扩展中的行为，包括：
 * - 设置按钮的点击
 * - Tab 页签的点击
 * - 列表项的点击
 * - 应用的点击
 */
export class AnalyticsHelper {
    private static instance: AnalyticsHelper;
    private extensionId: string = 'ai-resource';
    private extensionVersion: string = '1.0.0';

    private constructor() {
        // 统计功能始终启用
    }

    public static getInstance(): AnalyticsHelper {
        if (!AnalyticsHelper.instance) {
            AnalyticsHelper.instance = new AnalyticsHelper();
        }
        return AnalyticsHelper.instance;
    }

    /**
     * 初始化统计助手
     */
    public initialize(context: vscode.ExtensionContext): void {
        this.extensionId = context.extension.id;
        this.extensionVersion = context.extension.packageJSON.version || '1.0.0';

        Logger.info(`[AnalyticsHelper] 初始化完成，扩展ID: ${this.extensionId}, 版本: ${this.extensionVersion}`);
    }



    /**
     * 执行统计命令（内部方法）
     */
    private async executeAnalyticsCommand(command: string, ...args: any[]): Promise<void> {
        try {
            await vscode.commands.executeCommand(command, ...args);
        } catch (error) {
            // 静默失败，不影响扩展功能
            Logger.info(`[AnalyticsHelper] 统计命令执行失败: ${command} - ${error}`);
        }
    }

    /**
     * 追踪用户行为的通用方法
     */
    private async trackUserBehavior(behavior: string, action: string, target?: string, additionalData?: Record<string, any>): Promise<void> {
        const eventData = {
            extensionId: this.extensionId,
            extensionVersion: this.extensionVersion,
            behavior,
            action,
            target,
            timestamp: Date.now(),
            ...additionalData
        };

        await this.executeAnalyticsCommand('joycoder.analytics.trackUserBehavior',
            behavior, action, target, eventData);
    }

    /**
     * 追踪设置按钮点击
     * @param buttonType 按钮类型，如 'settings', 'refresh', 'login' 等
     * @param context 上下文信息，如所在页面、状态等
     */
    public async trackSettingsButtonClick(buttonType: string, context?: Record<string, any>): Promise<void> {
        Logger.info(`[AnalyticsHelper] 追踪设置按钮点击: ${buttonType}`);

        await this.trackUserBehavior('settings_button_click', 'click', buttonType, {
            buttonType,
            context: context || {},
            source: 'ai_resource_extension'
        });
    }

    /**
     * 追踪 Tab 页签点击
     * @param tabName Tab 页签名称
     * @param tabIndex Tab 页签索引（可选）
     * @param context 上下文信息
     */
    public async trackTabClick(tabName: string, tabIndex?: number, context?: Record<string, any>): Promise<void> {
        Logger.info(`[AnalyticsHelper] 追踪Tab页签点击: ${tabName}`);

        await this.trackUserBehavior('tab_click', 'click', tabName, {
            tabName,
            tabIndex,
            context: context || {},
            source: 'ai_resource_extension'
        });
    }

    /**
     * 追踪列表项点击
     * @param itemTitle 列表项标题
     * @param itemType 列表项类型，如 'model', 'plugin', 'resource' 等
     * @param itemId 列表项ID（可选）
     * @param context 上下文信息
     */
    public async trackListItemClick(itemTitle: string, itemType: string, itemId?: string, context?: Record<string, any>): Promise<void> {
        Logger.info(`[AnalyticsHelper] 追踪列表项点击: ${itemTitle} (${itemType})`);

        await this.trackUserBehavior('list_item_click', 'click', itemTitle, {
            itemTitle,
            itemType,
            itemId,
            context: context || {},
            source: 'ai_resource_extension'
        });
    }

    /**
     * 追踪应用点击
     * @param appTitle 应用标题
     * @param appType 应用类型，如 'ai_model', 'plugin', 'tool' 等
     * @param appId 应用ID（可选）
     * @param context 上下文信息
     */
    public async trackAppClick(appTitle: string, appType: string, appId?: string, context?: Record<string, any>): Promise<void> {
        Logger.info(`[AnalyticsHelper] 追踪应用点击: ${appTitle} (${appType})`);

        await this.trackUserBehavior('app_click', 'click', appTitle, {
            appTitle,
            appType,
            appId,
            context: context || {},
            source: 'ai_resource_extension'
        });
    }

    /**
     * 追踪扩展激活
     */
    public async trackExtensionActivation(activationTime?: number): Promise<void> {
        Logger.info(`[AnalyticsHelper] 追踪扩展激活`);

        await this.trackUserBehavior('extension_activation', 'activated', this.extensionId, {
            activationTime: activationTime || Date.now(),
            workspaceType: vscode.workspace.workspaceFolders ? 'folder' : 'file',
            folderCount: vscode.workspace.workspaceFolders?.length || 0
        });
    }

    /**
     * 追踪页面浏览
     * @param pageName 页面名称
     * @param pageType 页面类型
     */
    public async trackPageView(pageName: string, pageType: string): Promise<void> {
        Logger.info(`[AnalyticsHelper] 追踪页面浏览: ${pageName}`);

        await this.trackUserBehavior('page_view', 'view', pageName, {
            pageName,
            pageType,
            source: 'ai_resource_extension'
        });
    }

    /**
     * 追踪搜索行为
     * @param searchTerm 搜索词
     * @param searchType 搜索类型
     * @param resultCount 结果数量
     */
    public async trackSearch(searchTerm: string, searchType: string, resultCount?: number): Promise<void> {
        Logger.info(`[AnalyticsHelper] 追踪搜索: ${searchTerm} (${searchType})`);

        await this.trackUserBehavior('search', 'search', searchTerm, {
            searchTerm,
            searchType,
            resultCount,
            source: 'ai_resource_extension'
        });
    }

    /**
     * 追踪错误
     * @param error 错误对象或错误消息
     * @param context 错误上下文
     * @param severity 错误严重程度
     */
    public async trackError(error: Error | string, context: string, severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'): Promise<void> {
        const errorMessage = typeof error === 'string' ? error : error.message;
        Logger.info(`[AnalyticsHelper] 追踪错误: ${errorMessage}`);

        await this.executeAnalyticsCommand('joycoder.analytics.trackError',
            errorMessage, `${this.extensionId}.${context}`, severity);
    }
}
