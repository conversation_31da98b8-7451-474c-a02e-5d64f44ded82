/**
 * AI Resource 扩展命令常量
 * 统一管理所有注册和执行的命令名称
 */

// ================================
// AI Resources 扩展自定义命令
// ================================

/** 打开设置面板 */
export const AI_RESOURCES_OPEN_SETTINGS = 'aiResources.openSettings';

/** 选择资源 */
export const AI_RESOURCES_SELECT_RESOURCE = 'aiResources.selectResource';

/** 显示资源详情 */
export const AI_RESOURCES_SHOW_DETAIL = 'aiResources.showDetail';

/** 写入 API Key */
export const AI_RESOURCES_WRITE_API_KEY = 'aiResources.writeApiKey';

/** 贡献 API Key */
export const AI_RESOURCES_CONTRIBUTE_API_KEY = 'aiResources.getApiKey';

/** 刷新数据 */
export const AI_RESOURCES_REFRESH_DATA = 'aiResources.refreshData';

/** 刷新 webview */
export const AI_RESOURCES_REFRESH = 'aiResources.refresh';

/** 关闭设置面板 */
export const AI_RESOURCES_CLOSE_SETTINGS = 'aiResources.closeSettings';

// ================================
// JoyCode 核心命令
// ================================

/** 资源导入命令 */
export const JOYCODE_RESOURCE_IMPORT = 'joycode.Code.ResourceImport';

/** 生成应用 */
export const  JOYCODE_APP = 'joycode.joycoderEditor.applicationGeneration'

// ================================
// JoyCode IDE 相关命令
// ================================

/** JoyCode 登录命令 */
export const JOYCODE_LOGIN = 'workbench.action.joycoderLogin';

/** JoyCode 备用登录命令 */
export const JOYCODE_LOGIN_FALLBACK = 'joycode.login';

/** JoyCode 检查登录状态 */
export const JOYCODE_IS_LOGGED_IN = 'workbench.action.joycoderIsLoggedIn';

/** JoyCode 获取登录信息 */
export const JOYCODE_GET_LOGIN_INFO = 'workbench.action.joycoderGetLoginInfo';

/** JoyCode 登录状态变化通知 */
export const JOYCODE_LOGIN_STATUS_CHANGED = 'workbench.action.joycoderLoginStatusChanged';

// ================================
// VSCode 内置命令
// ================================

/** 显示 Markdown 预览 */
export const MARKDOWN_SHOW_PREVIEW = 'markdown.showPreview';

/** 打开文件夹 */
export const VSCODE_OPEN_FOLDER = 'vscode.openFolder';

/** 显示 AI Resources 视图 */
export const WORKBENCH_VIEW_AI_RESOURCES = 'workbench.view.extension.aiResourcesView';

// ================================
// 回调命令模式
// ================================

/** JoyCode 回调命令前缀 */
export const JOYCODE_CALLBACK_PREFIX = 'joycoder.callback.';

/** AI Resource 回调 ID */
export const AI_RESOURCE_CALLBACK_ID = 'joycoder.callback.resource';

// ================================
// 文档内容提供者 Scheme
// ================================

/** 插件详情文档 Scheme */
export const PLUGIN_DETAIL_SCHEME = 'plugin-detail';

/** AI Resource 详情文档 Scheme */
export const AI_RESOURCE_DETAIL_SCHEME = 'airesource-detail';

/** AI Resources 视图 ID */
export const AI_RESOURCES_VIEW_ID = 'aiResourcesView';

// ================================
// 命令工具函数
// ================================

/**
 * 生成 JoyCode 回调命令名称
 * @param callbackId 回调 ID
 * @returns 完整的回调命令名称
 */
export function generateJoyCodeCallbackCommand(callbackId: string): string {
	return `${JOYCODE_CALLBACK_PREFIX}${callbackId}`;
}

/**
 * 检查是否为 JoyCode 回调命令
 * @param commandId 命令 ID
 * @returns 是否为回调命令
 */
export function isJoyCodeCallbackCommand(commandId: string): boolean {
	return commandId.startsWith(JOYCODE_CALLBACK_PREFIX);
}

/**
 * 从回调命令中提取回调 ID
 * @param commandId 回调命令 ID
 * @returns 回调 ID，如果不是回调命令则返回 undefined
 */
export function extractCallbackId(commandId: string): string | undefined {
	if (isJoyCodeCallbackCommand(commandId)) {
		return commandId.substring(JOYCODE_CALLBACK_PREFIX.length);
	}
	return undefined;
}

// ================================
// 命令分组
// ================================

/** AI Resources 扩展的所有命令 */
export const AI_RESOURCES_COMMANDS = [
	AI_RESOURCES_OPEN_SETTINGS,
	AI_RESOURCES_SELECT_RESOURCE,
	AI_RESOURCES_SHOW_DETAIL,
	AI_RESOURCES_WRITE_API_KEY,
	AI_RESOURCES_CONTRIBUTE_API_KEY,
	AI_RESOURCES_REFRESH_DATA,
	AI_RESOURCES_REFRESH,
	AI_RESOURCES_CLOSE_SETTINGS,
] as const;

/** JoyCode IDE 相关的所有命令 */
export const JOYCODE_COMMANDS = [
	JOYCODE_APP,
	JOYCODE_LOGIN,
	JOYCODE_LOGIN_FALLBACK,
	JOYCODE_IS_LOGGED_IN,
	JOYCODE_GET_LOGIN_INFO,
	JOYCODE_LOGIN_STATUS_CHANGED,
] as const;

/** VSCode 内置命令 */
export const VSCODE_COMMANDS = [
	MARKDOWN_SHOW_PREVIEW,
	VSCODE_OPEN_FOLDER,
	WORKBENCH_VIEW_AI_RESOURCES,
] as const;

/** 所有命令的联合类型 */
export type AIResourceCommand = typeof AI_RESOURCES_COMMANDS[number];
export type JoyCodeCommand = typeof JOYCODE_COMMANDS[number];
export type VSCodeCommand = typeof VSCODE_COMMANDS[number];
export type AllCommands = AIResourceCommand | JoyCodeCommand | VSCodeCommand;
