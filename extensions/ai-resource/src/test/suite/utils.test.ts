import * as assert from 'assert';
import { maskKey } from '../../utils/mask';

suite('Utils Test Suite', () => {
	
	suite('maskKey', () => {
		test('should mask API key correctly', () => {
			const apiKey = 'abcdefghijklmnopqrstuvwxyz';
			const masked = maskKey(apiKey);
			
			// Should show first 4 and last 4 characters
			assert.strictEqual(masked, 'abcd****wxyz');
		});

		test('should handle short keys', () => {
			const shortKey = 'abc';
			const masked = maskKey(shortKey);
			
			// Should mask appropriately for short keys
			assert.strictEqual(masked, 'a*c');
		});

		test('should handle empty string', () => {
			const emptyKey = '';
			const masked = maskKey(emptyKey);
			
			assert.strictEqual(masked, '');
		});
	});
});
