import * as assert from 'assert';
import * as vscode from 'vscode';
import * as path from 'path';
import { JoycodeHelper } from '../../utils/joycodeHelper';

suite('JoycodeHelper Test Suite', () => {
	let testWorkspaceUri: vscode.Uri;
	let testApiKeyFileUri: vscode.Uri;

	suiteSetup(async () => {
		// 创建临时测试工作空间
		const tmpDir = path.join(__dirname, '../../../test-workspace');
		testWorkspaceUri = vscode.Uri.file(tmpDir);

		// 确保测试目录存在
		try {
			await vscode.workspace.fs.createDirectory(testWorkspaceUri);
		} catch (error) {
			// 目录可能已存在，忽略错误
		}

		// 设置测试用的 API Key 文件路径
		const aiResourceDir = vscode.Uri.joinPath(testWorkspaceUri, '.joycode', 'resources');
		testApiKeyFileUri = vscode.Uri.joinPath(aiResourceDir, 'apikey.json');
	});

	suiteTeardown(async () => {
		// 清理测试文件
		try {
			await vscode.workspace.fs.delete(testWorkspaceUri, { recursive: true });
		} catch (error) {
			// 忽略清理错误
		}
	});

	setup(async () => {
		// 每个测试前清理 API Key 文件
		try {
			await vscode.workspace.fs.delete(testApiKeyFileUri);
		} catch (error) {
			// 文件可能不存在，忽略错误
		}
	});

	suite('ensureApiKeyFile', () => {
		test('should create apikey.json with apikey only', async () => {
			const testApiKey = 'test-api-key-123';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				await JoycodeHelper.ensureApiKeyFile(testApiKey);

				// 验证文件是否创建
				const exists = await JoycodeHelper.exists(testApiKeyFileUri);
				assert.strictEqual(exists, true, 'API key file should be created');

				// 验证文件内容
				const content = await vscode.workspace.fs.readFile(testApiKeyFileUri);
				const json = JSON.parse(Buffer.from(content).toString('utf-8'));

				assert.strictEqual(json.apikey, testApiKey, 'API key should match');
				assert.strictEqual(json.baseUrl, undefined, 'baseUrl should not be set');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});

		test('should create apikey.json with apikey and baseUrl', async () => {
			const testApiKey = 'test-api-key-123';
			const testBaseUrl = 'https://test-api.example.com';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				await JoycodeHelper.ensureApiKeyFile(testApiKey, testBaseUrl);

				// 验证文件是否创建
				const exists = await JoycodeHelper.exists(testApiKeyFileUri);
				assert.strictEqual(exists, true, 'API key file should be created');

				// 验证文件内容
				const content = await vscode.workspace.fs.readFile(testApiKeyFileUri);
				const json = JSON.parse(Buffer.from(content).toString('utf-8'));

				assert.strictEqual(json.apikey, testApiKey, 'API key should match');
				assert.strictEqual(json.baseUrl, testBaseUrl, 'baseUrl should match');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});
	});

	suite('readConfig', () => {
		test('should read complete configuration', async () => {
			const testApiKey = 'test-api-key-456';
			const testBaseUrl = 'https://test-api-2.example.com';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				// 先创建文件
				await JoycodeHelper.ensureApiKeyFile(testApiKey, testBaseUrl);

				// 读取配置
				const config = await JoycodeHelper.readConfig();

				assert.ok(config, 'Config should be returned');
				assert.strictEqual(config!.apikey, testApiKey, 'API key should match');
				assert.strictEqual(config!.baseUrl, testBaseUrl, 'baseUrl should match');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});

		test('should return undefined for non-existent file', async () => {
			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				const config = await JoycodeHelper.readConfig();
				assert.strictEqual(config, undefined, 'Config should be undefined for non-existent file');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});
	});

	suite('updateApiKey', () => {
		test('should update apikey and preserve baseUrl', async () => {
			const initialApiKey = 'initial-api-key';
			const initialBaseUrl = 'https://initial-api.example.com';
			const newApiKey = 'new-api-key';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				// 先创建文件
				await JoycodeHelper.ensureApiKeyFile(initialApiKey, initialBaseUrl);

				// 更新 API Key
				await JoycodeHelper.updateApiKey(newApiKey);

				// 验证更新结果
				const config = await JoycodeHelper.readConfig();
				assert.ok(config, 'Config should exist');
				assert.strictEqual(config!.apikey, newApiKey, 'API key should be updated');
				assert.strictEqual(config!.baseUrl, initialBaseUrl, 'baseUrl should be preserved');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});

		test('should update both apikey and baseUrl', async () => {
			const initialApiKey = 'initial-api-key';
			const initialBaseUrl = 'https://initial-api.example.com';
			const newApiKey = 'new-api-key';
			const newBaseUrl = 'https://new-api.example.com';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				// 先创建文件
				await JoycodeHelper.ensureApiKeyFile(initialApiKey, initialBaseUrl);

				// 更新 API Key 和 baseUrl
				await JoycodeHelper.updateApiKey(newApiKey, newBaseUrl);

				// 验证更新结果
				const config = await JoycodeHelper.readConfig();
				assert.ok(config, 'Config should exist');
				assert.strictEqual(config!.apikey, newApiKey, 'API key should be updated');
				assert.strictEqual(config!.baseUrl, newBaseUrl, 'baseUrl should be updated');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});
	});

	suite('updateBaseUrl', () => {
		test('should update baseUrl and preserve apikey', async () => {
			const testApiKey = 'test-api-key';
			const initialBaseUrl = 'https://initial-api.example.com';
			const newBaseUrl = 'https://new-api.example.com';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				// 先创建文件
				await JoycodeHelper.ensureApiKeyFile(testApiKey, initialBaseUrl);

				// 更新 baseUrl
				await JoycodeHelper.updateBaseUrl(newBaseUrl);

				// 验证更新结果
				const config = await JoycodeHelper.readConfig();
				assert.ok(config, 'Config should exist');
				assert.strictEqual(config!.apikey, testApiKey, 'API key should be preserved');
				assert.strictEqual(config!.baseUrl, newBaseUrl, 'baseUrl should be updated');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});
	});

	suite('readBaseUrl', () => {
		test('should read baseUrl from file', async () => {
			const testApiKey = 'test-api-key';
			const testBaseUrl = 'https://test-api.example.com';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				// 先创建文件
				await JoycodeHelper.ensureApiKeyFile(testApiKey, testBaseUrl);

				// 读取 baseUrl
				const baseUrl = await JoycodeHelper.readBaseUrl();
				assert.strictEqual(baseUrl, testBaseUrl, 'baseUrl should match');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});

		test('should return undefined for file without baseUrl', async () => {
			const testApiKey = 'test-api-key';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				// 创建只有 apikey 的文件
				await JoycodeHelper.ensureApiKeyFile(testApiKey);

				// 读取 baseUrl
				const baseUrl = await JoycodeHelper.readBaseUrl();
				assert.strictEqual(baseUrl, undefined, 'baseUrl should be undefined');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});
	});

	suite('updateConfig', () => {
		test('should update complete configuration', async () => {
			const initialApiKey = 'initial-api-key';
			const initialBaseUrl = 'https://initial-api.example.com';
			const newApiKey = 'new-api-key';
			const newBaseUrl = 'https://new-api.example.com';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				// 先创建文件
				await JoycodeHelper.ensureApiKeyFile(initialApiKey, initialBaseUrl);

				// 更新完整配置
				await JoycodeHelper.updateConfig({
					apikey: newApiKey,
					baseUrl: newBaseUrl
				});

				// 验证更新结果
				const config = await JoycodeHelper.readConfig();
				assert.ok(config, 'Config should exist');
				assert.strictEqual(config!.apikey, newApiKey, 'API key should be updated');
				assert.strictEqual(config!.baseUrl, newBaseUrl, 'baseUrl should be updated');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});

		test('should create new file if not exists', async () => {
			const testApiKey = 'test-api-key';
			const testBaseUrl = 'https://test-api.example.com';

			// Mock getWorkspaceRootUri to return our test workspace
			const originalGetWorkspaceRootUri = JoycodeHelper.getWorkspaceRootUri;
			(JoycodeHelper as any).getWorkspaceRootUri = () => testWorkspaceUri;

			try {
				// 直接更新配置（文件不存在）
				await JoycodeHelper.updateConfig({
					apikey: testApiKey,
					baseUrl: testBaseUrl
				});

				// 验证文件是否创建
				const exists = await JoycodeHelper.exists(testApiKeyFileUri);
				assert.strictEqual(exists, true, 'Config file should be created');

				// 验证文件内容
				const config = await JoycodeHelper.readConfig();
				assert.ok(config, 'Config should exist');
				assert.strictEqual(config!.apikey, testApiKey, 'API key should match');
				assert.strictEqual(config!.baseUrl, testBaseUrl, 'baseUrl should match');
			} finally {
				// 恢复原始方法
				(JoycodeHelper as any).getWorkspaceRootUri = originalGetWorkspaceRootUri;
			}
		});
	});
});
