import * as vscode from "vscode";
import { selectData } from "./types";
import { JoycodeHelper } from './utils/joycodeHelper';
import { getJoyCodeConfig as getGlobalConfig, getCurrentBaseUrl as getGlobalBaseUrl } from './utils/globalContext';

/**
 * 内存中的认证信息管理
 */
class AuthCache {
	private static instance: AuthCache;
	private apiKey: string | undefined;
	private ptKey: string | undefined;
	private apiKeyList: selectData[] = [];

	private constructor() { }

	public static getInstance(): AuthCache {
		if (!AuthCache.instance) {
			AuthCache.instance = new AuthCache();
		}
		return AuthCache.instance;
	}

	public setApiKey(apiKey: string | undefined): void {
		this.apiKey = apiKey;
		console.log(`[AuthCache] 设置 API Key: ${apiKey ? `有值(长度:${apiKey.length})` : '空值'}`);
	}

	public getApiKey(): string | undefined {
		console.log(`[AuthCache] 获取 API Key: ${this.apiKey ? `有值(长度:${this.apiKey.length})` : '空值'}`);
		return this.apiKey;
	}

	public setPtKey(ptKey: string | undefined): void {
		this.ptKey = ptKey;
		console.log(`[AuthCache] 设置 PT Key: ${ptKey ? `有值(长度:${ptKey.length})` : '空值'}`);
	}

	public getPtKey(): string | undefined {
		console.log(`[AuthCache] 获取 PT Key: ${this.ptKey ? `有值(长度:${this.ptKey.length})` : '空值'}`);
		return this.ptKey;
	}

	public setApiKeyList(list: selectData[]): void {
		this.apiKeyList = list;
		console.log(`[AuthCache] 设置 API Key 列表: ${list.length} 个`);
	}

	public getApiKeyList(): selectData[] {
		console.log(`[AuthCache] 获取 API Key 列表: ${this.apiKeyList.length} 个`);
		return this.apiKeyList;
	}

	public clear(): void {
		this.apiKey = undefined;
		this.ptKey = undefined;
		this.apiKeyList = [];
		console.log(`[AuthCache] 已清空所有认证信息`);
	}

	public isReady(): boolean {
		const ready = !!(this.apiKey && this.ptKey);
		console.log(`[AuthCache] 认证信息就绪状态: ${ready}`);
		return ready;
	}
}

/**
 * 获取 JoyCode 配置信息
 * 从全局上下文中获取
 */
export function getJoyCodeConfig() {
	return getGlobalConfig();
}

let extensionContext: vscode.ExtensionContext | undefined;

/**
 * 初始化全局状态上下文，需在插件激活时调用一次
 */
export function initGlobalState(context: vscode.ExtensionContext) {
	extensionContext = context;
	const workspaceName = vscode.workspace.name || "unknown";
	console.log(`[config] initGlobalState 初始化工作区: ${workspaceName} (项目级别存储)`);
	return extensionContext;
}

/**
 * 获取当前的 extensionContext
 */
export function getExtensionContext(): vscode.ExtensionContext | undefined {
	return extensionContext;
}

/**
 * 存储 pt_key 到工作区状态（项目级别，每个项目独立）
 */
export async function setPtKey(ptKey: string) {
	const workspaceName = vscode.workspace.name || "unknown";
	console.log(`[config] setPtKey 工作区 ${workspaceName} 调用，设置 ptKey: ${ptKey ? '有值' : '空值'}`);

	if (!extensionContext) {
		console.error(`[config] setPtKey - extensionContext 未初始化！无法保存 PT Key`);
		return;
	}

	await extensionContext.workspaceState.update("pt_key", ptKey);
	console.log(`[config] setPtKey - 已调用 workspaceState.update`);

	// 验证设置是否成功
	const verifyKey = extensionContext.workspaceState.get<string>("pt_key");
	console.log(`[config] setPtKey 设置后验证，当前值: ${verifyKey ? '有值' : '空值'}`);
}

/**
 * 获取 pt_key（从内存缓存中获取）
 */
export function getPtKey(): string | undefined {
	return AuthCache.getInstance().getPtKey();
}

/**
 * 清空所有状态（清空工作区状态和内存缓存）
 */
export async function clearAllGlobalState() {
	const workspaceName = vscode.workspace.name || "unknown";
	console.log(`[config] clearAllGlobalState 开始清空工作区 ${workspaceName} 的状态`);

	// 清空内存缓存
	clearAuthCache();

	if (extensionContext) {
		// 清空当前工作区的所有状态
		const workspaceKeys = extensionContext.workspaceState.keys();
		for (const key of workspaceKeys) {
			await extensionContext.workspaceState.update(key, undefined);
		}
		console.log(`[config] clearAllGlobalState 已清空工作区状态，共 ${workspaceKeys.length} 个键`);

		// 同步写入到 .joycode/apikey.json
		await JoycodeHelper.updateApiKey('', '');
	}
}

/**
 * 存储当前正在使用的 apiKey（项目级别，每个项目独立）
 */
export async function setCurrentApiKey(apiKey: string) {
	const workspaceName = vscode.workspace.name || "unknown";
	console.log(`[config] setCurrentApiKey 工作区 ${workspaceName} 调用，设置 apiKey: ${apiKey ? '有值' : '空值'}`);
	console.log(`[config] setCurrentApiKey - extensionContext存在: ${!!extensionContext}`);

	if (!extensionContext) {
		console.error(`[config] setCurrentApiKey - extensionContext 未初始化！无法保存 API Key`);
		return;
	}

	await extensionContext.workspaceState.update("currentApiKey", apiKey);
	console.log(`[config] setCurrentApiKey - 已调用 workspaceState.update`);

	// 注意：不再自动写入项目文件，只有在引用时才写入
	// try {
	// 	// 同步写入到 .joycode/apikey.json
	// 	await JoycodeHelper.updateApiKey(apiKey);
	// 	console.log(`[config] setCurrentApiKey 成功写入文件`);
	// } catch (error) {
	// 	console.log(`[config] setCurrentApiKey 写入文件失败: ${error}`);
	// }

	// 验证设置是否成功
	const verifyKey = extensionContext.workspaceState.get<string>("currentApiKey");
	console.log(`[config] setCurrentApiKey 设置后验证，当前值: ${verifyKey ? '有值' : '空值'}`);

	// 调试：列出所有工作区状态键
	const allKeys = extensionContext.workspaceState.keys();
	console.log(`[config] setCurrentApiKey - 设置后工作区状态键: [${allKeys.join(', ')}]`);
}

/**
 * 设置当前正在使用的 apiKey（同时设置到内存和工作区状态）
 */
export async function setCurrentApiKeyToCache(apiKey: string): Promise<void> {
	// 设置到内存缓存
	AuthCache.getInstance().setApiKey(apiKey);

	// 同时保存到工作区状态（用于持久化）
	await setCurrentApiKey(apiKey);
}

/**
 * 设置 PT Key（同时设置到内存和工作区状态）
 */
export async function setPtKeyToCache(ptKey: string): Promise<void> {
	// 设置到内存缓存
	AuthCache.getInstance().setPtKey(ptKey);

	// 同时保存到工作区状态（用于持久化）
	await setPtKey(ptKey);
}

/**
 * 设置 API Key 列表到内存缓存
 */
export function setApiKeyListToCache(list: selectData[]): void {
	AuthCache.getInstance().setApiKeyList(list);
	// 同时保存到工作区状态
	setApiKeyList(list);
}

/**
 * 获取当前正在使用的 apiKey（从内存缓存中获取）
 */
export function getCurrentApiKey(): string | undefined {
	return AuthCache.getInstance().getApiKey();
}

/**
 * 获取当前的 baseUrl（从全局上下文中获取）
 */
export function getCurrentBaseUrl(): string {
	return getGlobalBaseUrl();
}

/**
 * 检查认证信息是否就绪
 */
export function isAuthReady(): boolean {
	return AuthCache.getInstance().isReady();
}

/**
 * 清空内存中的认证信息
 */
export function clearAuthCache(): void {
	AuthCache.getInstance().clear();
}

/**
 * 存储 apiKey 的数组（项目级别，每个项目独立）
 */
export function setApiKeyList(apiKeyList: selectData[]) {
	const workspaceName = vscode.workspace.name || "unknown";
	console.log(`[config] setApiKeyList 工作区 ${workspaceName} 调用，设置 ${apiKeyList.length} 个 apiKey`);
	extensionContext?.workspaceState.update("apiKeyList", apiKeyList);
}

/**
 * 获取 apiKey 的数组（项目级别，每个项目独立）
 */
export function getApiKeyList(): selectData[] | undefined {
	const list = extensionContext?.workspaceState.get<selectData[]>("apiKeyList");
	const workspaceName = vscode.workspace.name || "unknown";
	console.log(`[config] getApiKeyList 工作区 ${workspaceName} 调用，返回 ${list?.length || 0} 个 apiKey`);
	return list;
}
