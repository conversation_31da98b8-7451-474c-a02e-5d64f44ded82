// 通用响应数据结构
export interface ResponseData<T = any> {
	code: number;
	data: T;
	message: string;
}

// 分页查询参数
export interface PaginationParams {
	page: number;
	pageSize: number;
	keyword?: string;
}

// 分页响应数据结构
export interface PageResponse<T> {
	records: T[];
	total: number;
	size: number;
	current: number;
	orders: any[];
	optimizeCountSql: boolean;
	hitCount: boolean;
	countId: null;
	maxLimit: null;
	searchCount: boolean;
	pages: number;
}

// 用户相关类型
export interface UserInfo {
	id: number;
	username: string;
	email: string;
}

export interface LoginParams {
	username: string;
	password: string;
}

// 获取插件列表
export interface getListPageParams {
	pageNum: number;
	pageSize: number;
	// 模糊搜索
	query?: string;
	// 分类过滤
	catalog?: string;
}
// 获取插件列表 - 响应数据
export interface pluginResponse {
	id: number;
	name: string;
	pluginId: number;
	pluginDesc: string;
	iconUrl: string;
	pluginType: string;
	catalog: string;
	version: number;
	totalPv: number;
}

// 插件详情
export interface pluginDetail {
	id: number;
	name: string;
	username: string;
	pluginDesc: string;
	iconUrl: string;
	pluginType: string;
	tools: any[];
	version: number;
	mcpDesc: string | null;
	mcpType: string | number | null;
}

// 用户key
export interface userKey {
	id: number;
	userId: string;
	apiKey: string;
	authType: string;
	expiresTime: number;
	remark: string | null;
}

// 下拉数据
export interface selectData {
	value: string | number;
	label: string | number | any;
}

// 分类过滤选项
export interface CatalogOption {
	value: string;
	label: string;
}
