import * as vscode from "vscode";
import { AIResourcesViewProvider } from "./providers/aiResourcesViewProvider";
import { MarkdownProvider } from "./providers/markdownProvider";
import { SettingsWebviewPanel } from "./panels/settingsWebviewPanel";
import { AuthInitializer } from "./utils/authInitializer";
import { CommandManager } from "./utils/commandManager";
import {
	clearAllGlobalState,
	initGlobalState,
	getCurrentApiKey,
} from "./config";
import { Logger } from "./utils/logger";
import { I18n } from "./utils/i18n";
import { EnvironmentHelper, initializeGlobalConfig, setLoginContext } from "./utils/globalContext";
import { AnalyticsHelper } from "./utils/analyticsHelper";
import {
	AI_RESOURCES_REFRESH_DATA,
	AI_RESOURCES_REFRESH,
	AI_RESOURCES_CLOSE_SETTINGS,
	AI_RESOURCES_CONTRIBUTE_API_KEY,
	JOYCODE_LOGIN_STATUS_CHANGED,
	AI_RESOURCE_CALLBACK_ID,
	JOYCODE_IS_LOGGED_IN,
	PLUGIN_DETAIL_SCHEME,
	AI_RESOURCES_VIEW_ID
} from "./constants/commands";


interface ResourceProvider {
	refresh(): void;
}

/**
 * 检查JoyCode环境
 */
function checkJoyCodeEnvironment(): boolean {
	return EnvironmentHelper.validateJoyCodeEnvironment(true);
}

export async function activate(context: vscode.ExtensionContext) {
 
	// 初始化国际化
	await I18n.init(context);

	// 初始化统计助手
	const analytics = AnalyticsHelper.getInstance();
	analytics.initialize(context);

	// 记录环境信息
	EnvironmentHelper.logEnvironmentInfo();

	// 初始化全局状态
	initGlobalState(context);

	// 初始化全局配置上下文
	await initializeGlobalConfig();

	// 检查JoyCode环境
	const isJoyCode = checkJoyCodeEnvironment();

	// 创建核心组件
	const mdProvider = new MarkdownProvider();
	const webviewProvider = new AIResourcesViewProvider(context, mdProvider);
	const settingsPanel = new SettingsWebviewPanel(context);
	const authInitializer = AuthInitializer.getInstance();
	const commandManager = new CommandManager(context, settingsPanel);

	// 注册文档内容提供者
	context.subscriptions.push(
		vscode.workspace.registerTextDocumentContentProvider(PLUGIN_DETAIL_SCHEME, mdProvider)
	);

	// 注册WebView provider，启用缓存以避免静态资源重新加载
	context.subscriptions.push(
		vscode.window.registerWebviewViewProvider(AI_RESOURCES_VIEW_ID, webviewProvider, {
			webviewOptions: {
				retainContextWhenHidden: true
			}
		})
	);

	// 注册所有命令
	commandManager.registerCommands();

	// 注册AI资源列表刷新命令
	context.subscriptions.push(
		vscode.commands.registerCommand(AI_RESOURCES_REFRESH_DATA, async () => {
			Logger.info("[extension] 收到刷新AI资源列表命令");
			await webviewProvider.refreshData();
		})
	);

	// 注册简单的 webview 刷新命令
	context.subscriptions.push(
		vscode.commands.registerCommand(AI_RESOURCES_REFRESH, async () => {
			Logger.info("[extension] 收到刷新 webview 命令");
			await webviewProvider.refresh();
		})
	);

	// 注册关闭设置面板命令
	context.subscriptions.push(
		vscode.commands.registerCommand(AI_RESOURCES_CLOSE_SETTINGS, () => {
			Logger.info("[extension] 收到关闭设置面板命令");
			settingsPanel.close();
		})
	);

	// 注册贡献 API Key 命令
	context.subscriptions.push(
		vscode.commands.registerCommand(AI_RESOURCES_CONTRIBUTE_API_KEY, async () => {
			const apiKey = getCurrentApiKey();
			if (!apiKey) {
				vscode.window.showErrorMessage('apiKey获取失败');
				return '';
			};
			return apiKey;
		})
	);



	// 设置登录状态监听
	await setupLoginStatusHandling(context, authInitializer, webviewProvider, isJoyCode);

	

	// 追踪扩展激活
	await analytics.trackExtensionActivation();


}

/**
 * 设置登录状态处理
 */
async function setupLoginStatusHandling(
	context: vscode.ExtensionContext,
	authInitializer: AuthInitializer,
	webviewProvider: ResourceProvider,
	isJoyCode: boolean
): Promise<void> {
	const callbackId = AI_RESOURCE_CALLBACK_ID;

	// 监听IDE登录状态变化
	context.subscriptions.push(
		vscode.commands.registerCommand(JOYCODE_LOGIN_STATUS_CHANGED, async (params: any) => {
			Logger.info(`IDE登录状态变化: ${JSON.stringify(params)}`);

			// 处理登录状态变化并更新全局配置
			const result = await authInitializer.handleLoginStatusChange(params);
			Logger.info(`登录状态变化处理结果: ${JSON.stringify(result)}`);

			// 刷新 webview
			webviewProvider.refresh();
		})
	);

	// 注册登录状态变化回调
	context.subscriptions.push(
		vscode.commands.registerCommand(callbackId, async (params: any) => {

			const result = await authInitializer.handleLoginStatusChange(params);
			Logger.info(`登录状态变化处理结果: ${JSON.stringify(result)}`);
			webviewProvider.refresh();
		})
	);

	// 如果不是JoyCode环境，显示错误信息但仍然注册provider以显示错误UI
	if (!isJoyCode) {
		clearAllGlobalState();
		setLoginContext(false);
		webviewProvider.refresh();
		return;
	}

	// 只有在JoyCode环境下才检查登录状态
	try {
		Logger.info(`[extension] 开始检查登录状态，callbackId: ${callbackId}`);
		const isLoggedIn = await vscode.commands.executeCommand(JOYCODE_IS_LOGGED_IN, callbackId);
		Logger.info(`[extension] JOYCODE_IS_LOGGED_IN 检查结果: ${isLoggedIn}`);

		if (!isLoggedIn) {
			Logger.warn("用户未登录，显示登录界面");
			clearAllGlobalState();
			setLoginContext(false);
			webviewProvider.refresh();
		} else {
			Logger.info("[extension] 用户已登录，开始初始化认证信息");
			// 检查用户登录状态并初始化
			const result = await authInitializer.initialize();
			Logger.info(`扩展激活初始化结果: ${JSON.stringify(result)}`);

			if (result.isLoggedIn) {
				if (result.isReady) {
					Logger.info("用户已登录且认证信息就绪，刷新界面");
				} else {
					Logger.warn("用户已登录但认证信息未就绪");
				}
			} else {
				Logger.info("用户未登录，显示登录页面");
			}

			webviewProvider.refresh();
		}
	} catch (error) {
		Logger.error("检查登录状态失败：" + error);
		clearAllGlobalState();
		setLoginContext(false);
		webviewProvider.refresh();
	}
}

export function deactivate() { }


