import axios, {
	AxiosInstance,
	InternalAxiosRequestConfig,
	AxiosResponse,
} from "axios";
import { getCurrentApiKey, getPtKey } from "../config";
import { getCurrentBaseUrl } from "../utils/globalContext";
import { Logger } from "../utils/logger";

// 创建axios实例
const request: AxiosInstance = axios.create({
	baseURL: "", // 基础URL,根据实际环境配置
	timeout: 10000, // 请求超时时间
	headers: {
		"Content-Type": "application/json;charset=utf-8",
	},
});

// 请求拦截器
request.interceptors.request.use(
	(config: InternalAxiosRequestConfig) => {
		const token = getCurrentApiKey();
		const ptKey = getPtKey();

		// 动态获取配置并设置 baseURL（如果 URL 是相对路径）
		if (config.url && !config.url.startsWith('http')) {
			try {
				const baseURL = getCurrentBaseUrl();
				config.baseURL = baseURL;
				Logger.info(`[Request] 动态设置 baseURL: ${config.baseURL}`);
			} catch (error) {
				Logger.warn(`[Request] 获取配置失败，使用默认 baseURL: ${error instanceof Error ? error.message : String(error)}`);
			}
		}

		// 详细记录认证信息状态
		Logger.info(`[Request] 准备发送请求 ${config.method?.toUpperCase()} ${config.url}`);
		Logger.info(`[Request] 认证信息检查 - API Key: ${token}  ${token ? `已设置(长度:${token.length})` : '未设置'}, PT Key: ${ptKey ? `已设置(长度:${ptKey.length})` : '未设置'}`);

		if (token) {
			// 设置token到请求头
			config.headers.set("Authorization", `${token}`);
			Logger.info(`[Request] Authorization 头已设置`);
		} else {
			Logger.warn(`[Request] 警告: API Key 未设置，请求可能失败`);
		}

		if (ptKey) {
			// 设置ptKey到请求头
			config.headers.set("ptKey", `${ptKey}`);
			Logger.info(`[Request] ptKey 头已设置`);
		} else {
			Logger.warn(`[Request] 警告: PT Key 未设置，请求可能失败`);
		}

		return config;
	},
	(error) => {
		Logger.error(`[Request] 请求拦截器错误: ${error instanceof Error ? error.message : String(error)}`);
		return Promise.reject(error);
	},
);

// 响应拦截器
request.interceptors.response.use(
	(response: AxiosResponse) => {
		// 记录响应信息
		Logger.info(`${JSON.stringify(response.data)}, [Response] ${response.status} ${response.statusText} - URL: ${response.config.url}`);
		return response;
	},
	(error) => {
		// 详细的错误处理
		if (error.response) {
			// 服务器响应了错误状态码
			Logger.error(`[Response Error] ${error.response.status} ${error.response.statusText} - URL: ${error.config?.url}`);
			Logger.error(`[Response Error] Data: ${JSON.stringify(error.response.data)}`);
		} else if (error.request) {
			// 请求已发出但没有收到响应
			Logger.error(`[Network Error] 请求已发出但没有收到响应 - URL: ${error.config?.url}`);
		} else {
			// 其他错误
			Logger.error(`[Request Setup Error] ${error.message}`);
		}
		return Promise.reject(error);
	},
);

export default request;
