{"name": "resource", "displayName": "%displayName%", "description": "%description%", "version": "0.0.1", "outFloderName": "ai-resource", "engines": {"vscode": "^1.60.0"}, "categories": ["Other"], "extensionKind": ["ui", "workspace"], "extensionDependencies": ["joycode.joycode-common"], "main": "./out/extension.js", "publisher": "joycode", "activationEvents": [], "contributes": {"viewsContainers": {"activitybar": [{"id": "ai-resources", "title": "%viewContainer.title%", "icon": "$(joycode-ziyuanchajian)", "order": 4}]}, "views": {"ai-resources": [{"icon": "$(joycode-ziyuanchajian)", "type": "webview", "id": "aiResourcesView", "name": "%view.name%"}]}, "menus": {"view/title": [{"command": "aiResources.openSettings", "when": "view == aiResourcesView && aiResources.isLoggedIn == true", "group": "navigation"}]}, "commands": [{"command": "aiResources.openSettings", "title": "%command.openSettings.title%", "icon": "$(joycode-shezhi)"}, {"command": "workbench.view.extension.aiResourcesView", "title": "%command.showAiResources.title%"}], "configuration": {"title": "%displayName%", "properties": {"resource.joycodeDir": {"type": "string", "default": ".joycode", "description": "%config.joycodeDir.description%"}, "resource.apiKeyFile": {"type": "string", "default": "apikey.json", "description": "%config.apiKeyFile.description%"}}}}, "scripts": {"compile": "gulp compile-extension:resource", "watch": "gulp watch-extension:resource"}, "devDependencies": {"@types/glob": "^7.1.3", "@types/mocha": "^8.2.2", "@types/node": "14.x", "@types/vscode": "^1.60.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vscode/test-electron": "^1.6.2", "eslint": "^8.0.0", "glob": "^7.1.7", "mocha": "^8.4.0", "prettier": "^3.5.3", "rimraf": "^3.0.2", "typescript": "^4.9.0"}, "dependencies": {"axios": "^1.9.0"}}