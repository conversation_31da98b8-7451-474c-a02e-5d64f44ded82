<mxfile host="65bd71144e">
    <diagram id="cC-QrDTVbTBRmrMssTUW" name="第 1 页">
        <mxGraphModel dx="1233" dy="568" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="4" value="资源分类" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="990" y="170" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="资源列表（支持搜索）" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="990" y="250" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="资源详情" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="990" y="330" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="大模型资源" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="990" y="410" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="登录信息/用户信息" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="510" y="270" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="12" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="10" target="11">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="IDE信息" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="20" y="270" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="14" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="11" target="13">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="17" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="11" target="16">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="11" value="是不是 IDE 环境" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="210" y="260" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="结束" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="190" y="400" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="不是（非 IDE 环境不执行）" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="250" y="357.5" width="90" height="22.5" as="geometry"/>
                </mxCell>
                <mxCell id="20" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="16">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="414" y="400" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="22" style="edgeStyle=none;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="16" target="9">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="是否登录" style="rhombus;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="374" y="260" width="80" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="是" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="300" y="270" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="展示登录提示" style="ellipse;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="354" y="400" width="120" height="80" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="否" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="420" y="358" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="获取 apiKey" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="990" y="90" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="{&lt;div&gt;&amp;nbsp; pt_key: &#39;&#39;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; orgName: &#39;&#39;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; userName: &#39;&#39;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; env: &#39;&#39;,&lt;/div&gt;&lt;div&gt;&amp;nbsp; base_url: &#39;&#39;&lt;/div&gt;&lt;div&gt;}&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="500" y="185" width="190" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
