# Source files
src/**
!out/**

# Development files
.vscode/**
.vscode-test/**
.gitignore
.yarnrc
vsc-extension-quickstart.md
**/tsconfig.json
**/.eslintrc.json
**/.prettierrc
**/.prettierignore

# Build files
webpack.config.js
rollup.config.js

# Test files
**/test/**
**/*.test.ts
**/*.test.js

# Documentation (keep only essential)
docs/**
*.md
!README.md
!CHANGELOG.md

# Dependencies
node_modules/**
npm-debug.log
yarn-error.log

# Temporary files
**/*.map
.nyc_output
coverage/**

# OS files
.DS_Store
Thumbs.db

# IDE files
.idea/**
*.swp
*.swo

# Logs
*.log

# Package files
*.tgz
*.tar.gz

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache
.cache/**
.parcel-cache/**

# TypeScript
*.tsbuildinfo
