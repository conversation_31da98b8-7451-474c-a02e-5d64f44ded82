# 配置说明

## API 凭证配置

- 从 `.joycode/resources/apikey.json` 文件中读取用户的apiKey与baseUrl。
- 示例场景下，读取apikey.json文件，将apiKey与baseUrl直接写入代码中。
- 每次接口访问，都应在header中写入授权信息，例如：Authorization={apiKey}
- 请求时请拼接baseUrl
- 本接口有跨域访问限制

## 入参规则
- 入参外层需要使用"params"去包裹，示例：入参是query
```json
{
  params:{
      query:"测试"
  }
}
```
## 出参规则
- 数据返回结构体使用"data"包裹，示例：出参是log_id, data
```json
{
  code: 200,
  msg: "",
  data: {
    log_id: 'xxx',
    data: {}
  }
}
```

## 框架适配

需要根据项目使用的技术框架，将apiKey与baseUrl放入到项目配置文件中。

### 命名规范
- 命名空间: `joycode.resources`
- 配置项:
  - `joycode.resources.api_key`
  - `joycode.resources.base_url`

### 示例配置

#### Spring Boot 项目

在 `application.yml` 中配置：

```yaml
# application.yml
joycode:
  resources:
    api_key: your-api-key
    base_url: your-base-url
```

或在 `application.properties` 中配置：

```properties
# application.properties
joycode.resources.api_key=your-api-key
joycode.resources.base_url=your-base-url
```

其他框架请参照各自的配置规范进行相应配置。
