# 京东大模型网关接口文档

## 1. 概述
API KEY申请地址：[京东大模型API KEY申请地址](https://joyspace.jd.com/page/q9AY6VwZTxholVXl0E7D)，咚咚咨询群：10209877191。该模型接口兼容OpenAI标准。

### 1.1 服务地址
- **生产环境**: `http://gpt-proxy.jd.com`
- **高负载环境**: `http://gpt-proxy-ha.jd.com/`

## 2. 鉴权与配置

### 2.1 公共请求头（Headers）

| 字段名 | 是否必填 | 说明 |
|--------|----------|------|
| Authorization | 是 | 分配的apiKey信息 |
| accept | 否 | text/event-stream（流式时填写） |
| Content-Type | 是 | application/json |
| x-ms-client-request-id | 否 | 请求唯一标识，如果没有传会自动生成 |

### 2.2 公共请求体参数

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| erp | String | 否 | 发起请求的erp，用于费用计入相应部门 |
| bizApiKey | String | 否 | 业务自定义申请模型的apiKey（支持通道Maas、言犀） |

### 2.3 公共响应头（Response Headers）

| 字段名 | 说明 |
|--------|------|
| gw-request-id | 请求厂商唯一标识，用于排查模型厂商问题 |
| gw-h-id | 请求网关自动生成网关唯一标识 |

## 3. 支持的模型

模型详情参见：https://joyspace.jd.com/pages/3CPJC7kPeqm56rTuRVCz

| 厂商 | 模型名称 | 备注 |
|------|----------|------|
| 京东言犀 | Chatrhino-81B-T1 | 【京东推理模型，对标r1】 |
| 京东言犀 | Chatrhino-81B-Pro | 【推荐使用，最新版】 |
| 京东言犀 | Chatrhino-750B | 深度思考和非思考模式的混合模型，只需要在API调用言犀-750B时传入参数"chat_template_kwargs": {"enable_thinking": True/false}，即可进行思考模式和非思考模式切换.⚠️注意⚠️：750B深度思考模型的system role不能传输，需要传到user role 里 |
| 微软openAI | gpt-4.1 | 生成更简洁、更清晰的前端代码，自动检测现有代码库中需修改的关键部分，确保代码的可编译性以及成功率；支持单次输入100万token 的超长上下文，可完整解析复杂文档；擅长处理复合指令，执行完整性与准确性也所有提升，可更轻松地适配多样化应用场景,支持function call |
| 微软openAI | o3-mini | o3-mini是o1-mini的更新版本，快速便宜，为了满足不同场景的需求，o3-mini 创新地提供了低、中、高三种不同级别的“推理强度选项”（reasoning_effort：low、medium或high），总上下文200k，输出最大100k；支持function call；|
| 亚马逊 | claude-3-7-sonnet-v1 | 总上下文200k，非推理模式8k输出，推理模式64k输出 |
| 亚马逊 | anthropic.claude-sonnet-4-20250514-v1:0 | 总上下文200K，输出64K |
| 亚马逊 | anthropic.claude-opus-4-20250514-v1:0 | 总上下文200K，输出32K |
| 字节 | Doubao-1.5-vision-pro-32k-character | 总上下文32K，输出4K |
| 京东云 | DeepSeek-R1-jdcloud-iaas | 总上下文64K，输出4K |
| 京东云 | DeepSeek-V3-jdcloud-iaas | 总上下文64K，输出4K |

## 4. 接口详细说明

### 4.1 聊天完成接口（推荐）

**接口地址**: `POST http://gpt-proxy.jd.com/v1/chat/completions`

#### 4.1.1 请求参数

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| model | String | 是 | 所要调用的模型编码 |
| messages | List\<Object> | 是 | 对话信息列表 |
| stream | Boolean | 否 | 是否流式返回，默认false |
| temperature | Float | 否 | 采样温度，控制输出随机性 (0.0, 1.0) |
| top_p | Float | 否 | 核取样参数 (0.0, 1.0) |
| max_tokens | Integer | 否 | 模型输出最大tokens |
| tools | List | 否 | 可供模型调用的工具列表 |

#### 4.1.2 消息格式

**用户消息**:
```json
{
  "role": "user",
  "content": "你好"
}
```

**多模态消息**（支持图像）:
```json
{
  "role": "user",
  "content": [
    {
      "type": "text",
      "text": "解释一下图中的现象"
    },
    {
      "type": "image_url",
      "image_url": {
        "url": "https://xxx/xx.jpg",
        "detail": "high"
      }
    }
  ]
}
```

#### 4.1.3 Function Call工具调用

```json
{
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_weather",
        "description": "获取天气信息",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "城市名称"
            }
          },
          "required": ["location"]
        }
      }
    }
  ]
}
```

#### 4.1.4 请求示例

```json
{
  "erp": "zhangsan",
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ],
  "model": "gpt-4o-0806",
  "stream": false
}
```

#### 4.1.5 响应示例

**非流式响应**:
```json
{
  "id": "chatcmpl-8k5aBjXyPTyQdgpFqbhtvphcu1Z9b",
  "created": 1705995239,
  "model": "gpt-4o",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "你好！有什么我可以帮助你的吗？"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 18,
    "total_tokens": 27
  }
}
```

**流式响应**:
```
data: {"id":"chatcmpl-xxx","choices":[{"index":0,"delta":{"role":"assistant","content":"你好"}}]}
data: {"id":"chatcmpl-xxx","choices":[{"index":0,"delta":{"content":"！"}}]}
data: [DONE]
```

### 4.2 向量嵌入接口

**接口地址**: `POST http://gpt-proxy.jd.com/v1/embeddings`

#### 4.2.1 请求参数

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| model | String | 是 | 向量模型名称 |
| input | Object/String | 是 | 要向量化的文本内容 |

#### 4.2.2 请求示例

```json
{
  "erp": "zhangsan",
  "input": "需要向量化的文本内容",
  "model": "text-embedding-ada-002-2"
}
```

#### 4.2.3 响应示例

```json
{
  "model": "text-embedding-ada-002-2",
  "data": [
    {
      "embedding": [-0.02675454691052437, 0.019060475751757622, ...],
      "index": 0,
      "object": "embedding"
    }
  ],
  "usage": {
    "prompt_tokens": 4,
    "completion_tokens": 0,
    "total_tokens": 4
  }
}
```

### 4.3 图像生成接口

**接口地址**: `POST http://gpt-proxy.jd.com/v1/images/generations`

#### 4.3.1 请求参数

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| model | String | 是 | 图像生成模型名称 |
| prompt | String | 是 | 图像描述提示词 |
| size | String | 否 | 图像尺寸（如"1024x1024"） |
| n | Integer | 否 | 生成图像数量，默认1 |

#### 4.3.2 请求示例

```json
{
  "model": "cogview-3-plus",
  "erp": "zhangsan",
  "prompt": "生成一张五彩飞机图片"
}
```

#### 4.3.3 响应示例

```json
{
  "data": [
    {
      "url": "https://sfile.chatglm.cn/testpath/f0bbb5c0-ee87-543f-824d-3769d583f281_0.png"
    }
  ],
  "created": 1705923616
}
```

### 4.4 视频生成接口

#### 4.4.1 创建视频生成任务

**接口地址**: `POST http://gpt-proxy.jd.com/v1/videos/generations`

**请求参数**:

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| model | String | 是 | 视频生成模型名称 |
| prompt | String | 否 | 正向文本提示词 |
| image_url | String | 是 | 基础图像URL或Base64编码 |
| duration | String | 否 | 视频时长，支持"5"或"10"秒 |
| mode | String | 否 | 生成模式："std"（标准）或"pro"（高品质） |

#### 4.4.2 查询视频生成结果

**接口地址**: `POST http://gpt-proxy.jd.com/v1/videos/result`

**请求参数**:

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| model | String | 是 | 视频生成模型名称 |
| task_id | String | 是 | 任务ID |

### 4.5 搜索工具接口

**接口地址**: `POST http://gpt-proxy.jd.com/v1/web-search`

#### 4.5.1 请求参数

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| model | String | 是 | 搜索模型名称 |
| messages | List\<Object> | 是 | 搜索查询信息 |
| stream | Boolean | 否 | 是否流式返回 |
| page | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页数量，默认10，最大50 |

#### 4.5.2 支持的搜索模型

| 模型名称 | 说明 |
|----------|------|
| web-search-pro | 智谱搜索（支持流式） |
| search_std | 智谱基础版 |
| search_pro | 智谱高阶版 |
| search_pro_sogou | 搜狗搜索 |
| search_pro_quark | 夸克搜索 |
| search_bocha | 京东云智能搜索-博查 |

### 4.6 批处理API

#### 4.6.1 文件上传

**接口地址**: `POST http://gpt-proxy.jd.com/v1/files`

**请求参数**（FormData）:

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| file | File | 是 | 要上传的.jsonl文件（不超过100M） |
| purpose | String | 是 | 文件用途，当前仅支持"batch" |
| body | String | 是 | 额外参数，包含erp和model信息 |

#### 4.6.2 创建批处理任务

**接口地址**: `POST http://gpt-proxy.jd.com/v1/batches`

**请求参数**:

| 字段名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| input_file_id | String | 是 | 上传文件的ID |
| endpoint | String | 是 | 批处理使用的端点 |
| completion_window | String | 是 | 完成时间窗口，目前仅支持"24h" |

#### 4.6.3 查询批处理状态

**接口地址**: `GET http://gpt-proxy.jd.com/v1/batches/{batch_id}`

#### 4.6.4 下载批处理结果

**接口地址**: `GET http://gpt-proxy.jd.com/v1/files/{file_id}/content`

## 5. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0000 | 请求成功 |
| 1001 | 请求参数错误 |
| 1033 | 请求参数包含敏感词 |
| 1034 | 请求参数apiKey错误 |
| 1035 | 请求参数apiKey不能为空 |
| 1036 | 模型参数不能为空 |
| 1037 | 请求url错误 |
| 1050 | 今日免费试用额度已达上限 |
| 3001 | HTTP外部调用超时 |
| 3002 | 当前调用量已超过阈值 |
| 3004 | 调用触发限流 |
| 9002 | 当前请求数过多，稍后重试 |
| 9999 | 未知异常 |

## 6. 最佳实践

### 6.1 模型选择建议

1. **日常对话**：推荐使用 `gpt-4o-mini` 或 `Chatrhino-81B-Pro`
2. **复杂推理**：推荐使用 `o3-mini` 或 `Chatrhino-750B`
3. **图像理解**：推荐使用 `gpt-4o-0806` 或 `glm-4v-flash`
4. **长文本处理**：推荐使用 `gpt-4.1` 或 `glm-4-long`
5. **向量嵌入**：推荐使用 `text-embedding-3-large` 或 `embedding-3`

### 6.2 性能优化建议

1. **并发控制**：超过50并发请求时使用高负载环境地址
2. **流式输出**：对于实时交互场景，建议使用流式输出
3. **批处理**：对于大量非实时请求，建议使用Batch API（价格为正常调用的50%）
4. **缓存策略**：对于相同的向量化请求，建议实现本地缓存
