# DeepSeek-V3

## 介绍

京东云部署的DeepSeek-V3大模型，该模型接口兼容OpenAI接口协议标准。

## 接口 URL

`POST {baseUrl}/api/saas/openai-u/v1/chat/completions`

## 请求参数

### Headers
| 参数名   | 类型   | 是否必传 | 描述     |
| -------- | ------ | -------- | -------- |
| Authorization | string | 是       | {apiKey} |

### Body
| 参数名 | 类型 | 是否必填 | 说明 |
|--------|------|----------|------|
| model | String | 是 | 所要调用的模型编码，此处填写固定值 DeepSeek-V3-jdcloud |
| messages | List\<Object> | 是 | 对话信息列表 |
| - role  | string | 否       |      |
| - content | string | 是       |  |
| stream | Boolean | 否 | 是否流式返回，默认false |
| temperature | Float | 否 | 采样温度，控制输出随机性 (0.0, 1.0) |
| top_p | Float | 否 | 核取样参数 (0.0, 1.0) |
| max_tokens | Integer | 否 | 模型输出最大tokens |
| tools | List | 否 | 可供模型调用的工具列表 |

**用户消息**:
```json
{
  "role": "user",
  "content": "你好"
}
```

**多模态消息**（支持图像）:
```json
{
  "role": "user",
  "content": [
    {
      "type": "text",
      "text": "解释一下图中的现象"
    },
    {
      "type": "image_url",
      "image_url": {
        "url": "https://xxx/xx.jpg",
        "detail": "high"
      }
    }
  ]
}
```

**Function Call工具调用**

```json
{
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "get_weather",
        "description": "获取天气信息",
        "parameters": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "城市名称"
            }
          },
          "required": ["location"]
        }
      }
    }
  ]
}
```

## 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| created | int | 创建时间戳 |
| usage | object | 使用情况统计 |
| - completion_tokens | int | 完成的令牌数 |
| - prompt_tokens | int | 提示的令牌数 |
| - total_tokens | int | 总令牌数 |
| model | string | 模型名称 |
| id | string | 响应ID |
| choices | array | 选择项 |
| - finish_reason | string | 结束原因 |
| - index | int | 选择项索引 |
| - message | object | 消息内容 |
| -- role | string | 角色（assistant） |
| -- content | string | 内容 |


## 示例

#### 请求示例

```json
{
  "messages": [
    {
      "role": "user",
      "content": "你好"
    }
  ],
  "model": "DeepSeek-V3-jdcloud",
  "stream": false
}
```

#### 响应示例

**非流式响应**:
```json
{
  "id": "chatcmpl-8k5aBjXyPTyQdgpFqbhtvphcu1Z9b",
  "created": 1705995239,
  "model": "DeepSeek-V3-jdcloud",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "你好！有什么我可以帮助你的吗？"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 9,
    "completion_tokens": 18,
    "total_tokens": 27
  }
}
```

**流式响应**:
```
data: {"id":"chatcmpl-xxx","choices":[{"index":0,"delta":{"role":"assistant","content":"你好"}}]}
data: {"id":"chatcmpl-xxx","choices":[{"index":0,"delta":{"content":"！"}}]}
data: [DONE]
```
