# {0}

## 介绍

该模型接口兼容openAI标准。

## 接口 URL

`POST {baseUrl}/api/saas/openai-u/v1/chat/completions`

## 请求参数

### Headers
| 参数名   | 类型   | 是否必传 | 描述     |
| -------- | ------ | -------- | -------- |
| Authorization | string | 是       | {apiKey} |

### Body
| 参数名   | 类型   | 是否必传 | 描述     |
| -------- | ------ | -------- | -------- |
| message  | object | 是       |      |
| - role  | string | 否       |      |
| - content | string | 是       |  |
| model | string | 是       |  |


## 响应参数

| 参数名 | 类型 | 描述 |
|--------|------|------|
| created | int | 创建时间戳 |
| usage | object | 使用情况统计 |
| - completion_tokens | int | 完成的令牌数 |
| - prompt_tokens | int | 提示的令牌数 |
| - total_tokens | int | 总令牌数 |
| model | string | 模型名称 |
| id | string | 响应ID |
| choices | array | 选择项 |
| - finish_reason | string | 结束原因 |
| - index | int | 选择项索引 |
| - message | object | 消息内容 |
| -- role | string | 角色（assistant） |
| -- content | string | 内容 |
